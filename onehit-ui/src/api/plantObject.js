import { createAxios } from '@/common/axios'
import baseUrls from './baseUrlConfig'

const axios = createAxios(baseUrls.edc)
import <PERSON><PERSON>r<PERSON>and<PERSON> from '@/common/ErrorHandler'
import { dataFlowItemApi } from './dataFlow'

export const plantObjectApi = {
  /**
   * 获取工厂对象所在的PID
   * @param projectSpaceId
   * @param numbering
   * @returns {Promise<[AimsDocument]> || []}
   */
  async getPIDs (projectSpaceId, numbering) {
    if (!(projectSpaceId && numbering)) {
      return []
    }

    const url = `/projectSpaces/${projectSpaceId}/plantObjects/relation/pids`
    return axios.get(url, {
      params: {
        numbering: numbering
      }
    }).then(({ data }) => {
      return data
    }).catch(err => {
      ErrorHandler.formatError(err)
    })
  },

  getPIDByRepoIds (repositoryIds, numbering) {
    if (!(repositoryIds && numbering)) {
      return []
    }
    const params = {
      repositoryIds: repositoryIds,
      numbering: numbering
    }
    return axios.post('/plantObjects/relation/pids', params, { isFormData: false })
  },

  /**
   * 检查工厂对象是否存在某张PID上
   * @param projectSpaceId
   * @param numbering
   * @returns {Promise<[AimsDocument]> || []}
   */
  async hasPIDs (projectSpaceId, numbering) {
    if (!(projectSpaceId && numbering)) {
      return false
    }

    const url = `/projectSpaces/${projectSpaceId}/plantObjects/relation/pids/isExist`
    return axios.get(url, {
      params: {
        numbering: numbering
      }
    }).then(({ data }) => {
      return data
    }).catch(err => {
      ErrorHandler.formatError(err)
    })
  },

  /**
   * 获取itemId所属的工厂对象
   * @param repoId
   * @param itemId
   * @returns {Promise<null|{
   *   numbering: **,
   *   itemId: **
   * }>}
   */
  async getBelongPlantObject (repoId, itemId) {
    if (!(itemId && repoId)) {
      return {}
    }
    const ancestors = await dataFlowItemApi.getAncestors(repoId, itemId)
    const itemIdToNumbering = await plantObjectApi.getNumberingByItemIds(repoId, ancestors)
    for (const itemId of ancestors) {
      if (itemIdToNumbering[itemId]) {
        return {
          numbering: itemIdToNumbering[itemId],
          itemId: itemId,
          repositoryId: repoId
        }
      }
    }
    return {}
  },

  /**
   * 获取item以及它祖先各自对应的numbering
   * @param repoId
   * @param itemId
   * @returns {Promise<*[]>}
   */
  async getAllBelongPONumbering (repoId, itemId) {
    if (!(itemId && repoId)) {
      return []
    }
    const ancestors = await dataFlowItemApi.getAncestors(repoId, itemId)
    const itemIdToNumbering = await dataFlowItemApi.getNumberingByItemIds(repoId, ancestors)
    if (!itemIdToNumbering) {
      return []
    }
    const numberingArr = []
    for (const itemId of ancestors) {
      if (itemIdToNumbering[itemId]) {
        numberingArr.push(itemIdToNumbering[itemId])
      }
    }
    return numberingArr
  },

  /**
   * 获取item对应的numbering
   * @param itemId
   * @returns {Promise<{}|无序
   * {
   *   itemId: numbering,
   *   itemId: numbering
   * }>}
   */
  async getNumberingByItemIds (repoId, itemIds) {
    if (repoId && Array.isArray(itemIds) && itemIds.length > 0) {
      return axios.post(`/repositories/${repoId}/es/mget/numbering`, itemIds, {
        isFormData: false
      }).then(({ data }) => {
        return data
      }).catch(err => {
        ErrorHandler.formatError(err)
      })
    } else {
      return {}
    }
  },

  /**
   * 根据numbering获取itemId
   * @param repositoryId
   * @param numbering
   * @returns {Promise<null|[itemId, itemId, itemId]>}
   */
  async getItemIdsByNumbering (repositoryId, numbering) {
    if (!(repositoryId && numbering)) {
      return []
    }
    return axios.get('/es/documents/filters', {
      params: {
        repositoryId: repositoryId,
        numbering: numbering
      }
    }).then(({ data }) => {
      return data
    }).catch(err => {
      ErrorHandler.formatError(err)
    })
  },

  /**
   * 获取工厂对象id
   * @param psId
   * @param numbering
   * @returns {Promise<null|AxiosResponse<any>>}
   */
  async getPlantObjectId (psId, numbering) {
    if (!(psId && numbering)) {
      return null
    }

    const url = `/projectSpaces/${psId}/plantObjects/id`
    return axios.get(url, {
      params: {
        numbering
      }
    }).then(({ data }) => {
      return data
    }).catch(err => {
      ErrorHandler.formatError(err)
    })
  },

  /**
   * 获取工厂对象的props
   * @param psId
   * @param poId
   * @returns {Promise<*[]|AxiosResponse<any>>}
   */
  async getPlantObjectProps (psId, poId) {
    if (!(psId && poId)) {
      return []
    }

    const url = `/projectSpaces/${psId}/plantObjects/${poId}/properties`
    return axios.get(url).then(({ data }) => {
      return data
    }).catch(err => {
      ErrorHandler.formatError(err)
    })
  },

  /**
   * 根据numbering获取已经交付的移交文档
   * @param psId
   * @param numbering
   * @returns {Promise<Map<categoryNumber, [document, document]>>}
   */
  async getPlantObjectDocuments (psId, poId) {
    if (!(psId && poId)) {
      return new Map()
    }

    const url = `/projectSpaces/${psId}/plantObjects/${poId}/actualDeliveryDocuments`
    return axios.get(url).then(({ data }) => {
      return data
    }).catch(err => {
      ErrorHandler.formatError(err)
    })
  },

  /**
   * 获取工厂对象设备模型的handle
   * @param psId
   * @param numbering
   * @returns {Promise<*[]|AxiosResponse<any>>}
   */
  async getPlantObjectDeviceHandles (repositoryIds, numbering) {
    if (!(repositoryIds && numbering)) {
      return []
    }
    const params = {
      repositoryIds,
      numbering
    }
    return axios.post('/deviceModels/handles', params, { isFormData: false })
  }
}
