import { createAxios } from '@/common/axios'
import baseUrls from './baseUrlConfig'

const axios = createAxios(baseUrls.edc)

/**
 * 智能P&ID
 */
export const viewPIDApi = {
  /**
   * 获取导航树节点
   * @param { string } repoId
   * @returns { Promise <{data: string|null}> }
   */
  getTreeNodes (repoId) {
    if (!repoId) {
      return Promise.resolve({ data: null })
    }
    return axios.get(`/repositories/${repoId}/pids`)
  }
}
