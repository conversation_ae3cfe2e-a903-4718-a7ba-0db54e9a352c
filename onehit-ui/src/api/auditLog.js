import { createAxios } from '@/common/axios'
import baseUrls from './baseUrlConfig'

const axios = createAxios(baseUrls.edc)

export const loginLogApi = {
  /**
   * 获取登录日志
   * @param params
   * @returns {*}
   */
  getLoginLog (params) {
    return axios.get('/log/login/events', { params, cancelKey: 'cancelKey' })
  }
}

export const changeRecordApi = {
  /**
   * 获取变更记录
   * @param body
   * @returns {*}
   */
  getChangeRecord (body) {
    return axios.post('/changelog', body, { isFormData: false, cancelKey: 'cancelKey' })
  },
  /**
   * 获取记录中有关联的项目空间
   * @returns {*}
   */
  getProjectSpace () {
    return axios.get('/changelog/projectSpace', { cancelKey: 'cancelKey' })
  },
  /**
   * 获取事件对象
   * @returns {*}
   */
  getEventObject () {
    return axios.get('/changelog/eventObject', { cancelKey: 'cancelKey' })
  }
}
