import { createAxios } from '@/common/axios'
import baseUrls from './baseUrlConfig'

const axios = createAxios(baseUrls.uams)

/**
 * 用户管理
 */
export const userManagementApi = {
  /**
   * 获取内部部门
   * @param params
   * @returns {Promise<unknown>}
   */
  getDepartment (params) {
    return axios.get('/dept/manager/departments', { params, cancelKey: 'cancelKey' })
  },
  /**
   * 以列表形式获取所有内部部门
   * @returns {Promise<AxiosResponse<any>>}
   */
  getDepartmentList () {
    return axios.get('/dept/manager/allDepartments')
  },
  /**
   * 获取内部部门筛选列表
   * @param body
   * @returns {Promise<AxiosResponse<any>>}
   */
  getDepartmentByFilterList (body) {
    return axios.post('/dept/manager/departmentTree/search', body, { isFormData: false, cancelKey: 'cancelKey' })
  },
  /**
   * 获取所有内部部门id与用户id集合的映射
   * @returns {Promise<AxiosResponse<any>>}
   */
  getDepartmentsToUserIds () {
    return axios.get('/dept/manager/departmentsToUserIds')
  },
  /**
   * 获取协同公司
   * @param body
   * @returns {Promise<unknown>}
   */
  getCollaborativeCompany (body) {
    return axios.post('/cooperateCompanies/filter', body, { isFormData: false, cancelKey: 'cancelKey' })
  },
  /**
   * 获取公司内部用户
   * @param body
   * @returns {Promise<AxiosResponse<any>>}
   */
  getInternalUser (body) {
    return axios.post('/user/manager/users/internal', body, { isFormData: false, cancelKey: 'cancelKey' })
  },
  /**
   * 获取协同公司用户
   * @param body
   * @returns {Promise<AxiosResponse<any>>}
   */
  getCollaborativeUser (body) {
    return axios.post('/user/manager/users/cooperate', body, { isFormData: false, cancelKey: 'cancelKey' })
  },
  /**
   * 检查用户名是否存在
   * @param params
   * @returns {Promise<AxiosResponse<any>>}
   */
  checkUserNameExist (params) {
    return axios.get('/user/manager/users/userName/check', { params })
  },
  /**
   * 检查手机号是否存在
   * @param params
   * @returns {Promise<AxiosResponse<any>>}
   */
  checkMobilePhoneExist (params) {
    return axios.get('/user/manager/users/mobilePhone/check', { params })
  },
  /**
   * 新增用户
   * @param body
   * @returns {Promise<AxiosResponse<any>>}
   */
  addUser (body) {
    return axios.post('/user/manager/user', body, { isFormData: false })
  },
  /**
   * 编辑用户
   * @param userId
   * @param body
   * @returns {Promise<AxiosResponse<any>>}
   */
  editUser (userId, body) {
    return axios.put(`/user/manager/user/${userId}`, body, { isFormData: false })
  },
  /**
   * 导入公司内部用户发现新公司
   * @param params
   * @param body
   * @returns {*}
   */
  autoAddNewDept (params, body) {
    return axios.post('/user/manager/user/internal/import/discoverNewDept', body, {
      isFormData: false,
      params
    })
  },
  /**
   * 导入协同公司用户发现新公司
   * @param params
   * @param body
   * @returns {*}
   */
  autoAddNewComp (params, body) {
    return axios.post('/user/manager/user/cooperate/import/discoverNewDept', body, {
      isFormData: false,
      params
    })
  },
  /**
   * 检查是否能够删除用户
   * @param body
   * @returns {Promise<AxiosResponse<any>>}
   */
  checkEnableDelete (body) {
    return axios.post('/deleteUsers/check', body, { isFormData: false })
  },
  /**
   * 删除用户
   * @param data
   * @returns {Promise<AxiosResponse<any>>}
   */
  deleteUsers (data) {
    return axios.delete('/user/manager/users', { data })
  },
  /**
   * 变更部门
   * @param body
   * @returns {Promise<AxiosResponse<any>>}
   */
  assignmentDepartmentApi (body) {
    return axios.post('/user/manager/users/departments/distribute', body, { isFormData: false })
  },
  /**
   * 变更角色
   * @param body
   * @returns {Promise<AxiosResponse<any>>}
   */
  assignmentRoleApi (body) {
    return axios.put('/users/roles/relation',
      body, { isFormData: false })
  },
  /**
   * 重置密码
   * @param userId
   * @returns {Promise<AxiosResponse<any>>}
   */
  resetPasswordApi (userId) {
    return axios.put(`/user/manager/users/${userId}/resetPassword`)
  }
}
