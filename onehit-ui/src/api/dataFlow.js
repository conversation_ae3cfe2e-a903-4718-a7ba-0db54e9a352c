import { createAxios } from '@/common/axios'
import baseUrls from './baseUrlConfig'
import <PERSON><PERSON>r<PERSON><PERSON><PERSON> from '@/common/ErrorHandler'

const axios = createAxios(baseUrls.aims)
const axiosEDC = createAxios(baseUrls.edc)

export const dataFlowApi = {
  /**
   * 在EDC 3.8.0 以后EDC-server增加了此接口
   * 随机获取一个handle
   * @param repoId
   * @returns {null|Promise<String>}
   */
  randomOneHandle (repoId) {
    if (!repoId) {
      return null
    }
    const url = `/repositories/${repoId}/handles/randomOne`
    return axios.get(url).then(({ data }) => {
      return Promise.resolve(data)
    })
  },

  /**
   * 获取模板
   * @param type
   * @returns {Promise<Array<Template>>}
   */
  getTemplates (type = 'Item') {
    if (!type) {
      return null
    }

    return axios.get('/structures/templates', {
      params: { type }
    }).then(({ data }) => Promise.resolve(data))
      .catch(err => ErrorHandler.formatError(err))
  },

  /**
   * 更新模板
   * @param id
   * @param name
   * @returns {Promise<void>}
   */
  updateTemplate (id, name) {
    if (!(id && name)) {
      return
    }
    return axios.put(`/structures/templates/${id}`, {
      name: name
    }, {
      isFormData: false
    })
  },

  /**
   * 新增模板
   * @param name
   * @param type
   * @returns {Promise<templateId>}
   */
  addTemplate (name, type) {
    if (!(name && type)) {
      return null
    }
    return axios.post('/structures/templates', {
      name
    }, {
      isFormData: false,
      params: {
        type: 'Document'
      }
    })
  },

  /**
   * 删除模板
   * @param id
   * @returns {Promise<AxiosResponse<any>>}
   */
  deleteTemplate (id) {
    if (!id) {
      return
    }
    return axios.delete(`/structures/templates/${id}`)
  },

  /**
   * 保存模板
   * @param template
   * @param structure
   * @returns {Promise<void>}
   */
  saveTemplate (template, structure) {
    axios.put(`/structures/templates/${template.id}`, {
      name: template.name,
      contents: structure
    }, {
      isFormData: false
    })
  },

  /**
   * 获取仓库
   * @returns {Promise<unknown | void>}
   */
  getRepositories () {
    return axios.get('/repositories').then(({ data }) => Promise.resolve(data))
      .catch(err => ErrorHandler.formatError(err))
  },

  /**
   * 获取目录树的根节点
   * @param repositoryId
   * @param templateId
   * @param taskId
   * @returns {Promise<unknown | void>}
   */
  getRootOfStructureNodes (repositoryId, templateId, taskId, versionId = 'latest') {
    return axios.get(`/repositories/${repositoryId}/versions/${versionId}/structures/structureNodes/root`, {
      params: {
        taskId: taskId,
        templateId: templateId
      }
    }).then(({ data }) => Promise.resolve(data))
      .catch(err => ErrorHandler.formatError(err))
  },

  /**
   * 获取节点的孩子节点
   * @param repositoryId
   * @param parentNodeId
   * @returns {Promise<unknown | void>}
   */
  getChildrenOfStructureNode (repositoryId, parentNodeId) {
    const url = `/repositories/${repositoryId}/structures/structureNodes/${parentNodeId}/children`
    return axios.get(url).then(({ data }) => Promise.resolve(data))
      .catch(err => ErrorHandler.formatError(err))
  },

  // 性能太差，换异步加载了
  // getPosterityOfStructureNode (repositoryId, parentNodeId) {
  //   return axios.get(`/repositories/${repositoryId}/structures/structureNodes/${parentNodeId}/posterity`, {
  //     baseURL: '/AIMS'
  //   }).then(({ data }) => Promise.resolve(data))
  //     .catch(err => ErrorHandler.formatError(err))
  // }
  /**
   * 获取item的handle
   * @param repositoryId
   * @param documentId
   * @param itemIds
   * @returns {Promise<unknown | void>}
   */
  getHandlesOfItems (repositoryId, documentId, itemIds) {
    return axios.post(`/repositories/${repositoryId}/references/bulk`, {
      sourceDocumentId: documentId,
      targetItemIds: JSON.stringify(itemIds)
    }, {
    }).then(({ data }) => Promise.resolve(data))
      .catch(err => ErrorHandler.formatError(err))
  },

  /**
   * 根据itemId获取对应的组织节点
   * @param repositoryId
   * @param itemId
   * @param templateId
   * @returns {Promise<unknown | void>}
   */
  getStructureNodeOfItemId (repositoryId, itemId, templateId, versionId = 'latest') {
    return axios.get(`/repositories/${repositoryId}/versions/${versionId}/structures/itemId/${itemId}/structureNodes`, {
      params: {
        templateId
      }
    }).then(({ data }) => Promise.resolve(data))
      .catch(err => ErrorHandler.formatError(err))
  },

  /**
   * 获取组织节点的层层父节点 [自己，父，爷，...]
   * @param repositoryId
   * @param structureNodeId
   * @returns {Promise<unknown | void>}
   */
  getStructureNodeAncestors (repositoryId, structureNodeId, versionId = 'latest') {
    return axios.get(`/repositories/${repositoryId}/versions/${versionId}/structures/structureNodes/${structureNodeId}/ancestors`)
      .then(({ data }) => Promise.resolve(data))
      .catch(err => ErrorHandler.formatError(err))
  }
}

export const dataFlowItemApi = {
  /**
   * 获取对象对比结果
   * @param repositoryId
   * @param versionId
   * @returns {*}
   */
  getObjectDiff (repositoryId, versionId) {
    return axios.get(`/repositories/${repositoryId}/versions/${versionId}/items/diff`)
      .then(({ data }) => Promise.resolve(data))
      .catch(err => ErrorHandler.formatError(err))
  },
  /**
   * 获取属性对比结果
   * @param repositoryId
   * @param versionId
   * @returns {*}
   */
  getPropertiesDiff (repositoryId, versionId) {
    return axios.get(`/repositories/${repositoryId}/versions/${versionId}/items/properties/diff`)
      .then(({ data }) => Promise.resolve(data))
      .catch(err => ErrorHandler.formatError(err))
  },
  /**
   * 获取仓库的Psata
   * @param repositoryId
   * @returns {Promise<null|{
   *  "id": "56f12091-ce2c-4978-9fd4-0b7df3fb2257",
   *  "repositoryId": "c4085e14-add2-4d38-bdc8-0212ec2d0da4",
   *  "name": "5926920A-9815-4730-95BE-A178EA83BCC5.pgf"
   * }>}
   */
  async getPsData (repositoryId) {
    if (!repositoryId) {
      return null
    }
    return axios.get(`/repositories/${repositoryId}/psdata`)
      .then(response => {
        return response.data
      }).catch(err => {
        if (err.response.status === 404) {
          return null
        } else {
          ErrorHandler.formatError(err)
        }
      })
  },

  /**
   * 获取祖先有序，自己->爸爸->爷爷
   * @param repoId
   * @param itemId
   * @returns {Promise<[] | [itemId, itemId]>}
   */
  async getAncestors (repoId, itemId) {
    if (!(repoId && itemId)) {
      return []
    }
    return axios.get(`/repositories/${repoId}/relationships/${itemId}/composition`)
      .then(response => {
        return response.data.map(ship => ship.itemId)
      }).catch(err => {
        ErrorHandler.formatError(err)
      })
  },

  /**
   * 获取item对应的handles
   * @param repositoryId
   * @param itemId
   * @returns {Promise<[]|[handle, handle]>}
   */
  async getHandles (repositoryId, itemId) {
    if (!(repositoryId && itemId)) {
      return []
    }
    const psData = await dataFlowItemApi.getPsData(repositoryId)
    if (psData) {
      return axios.get(`/repositories/${repositoryId}/references`, {
        params: {
          targetItemId: itemId,
          sourceDocumentId: psData.id
        }
      }).then(response => {
        return response.data.flatMap(ref => ref.handles)
      })
    } else {
      return []
    }
  },

  /**
   * 根据handle获取itemId
   * @param repositoryId
   * @param handle
   * @returns {Promise<null|itemId>}
   */
  async getItemIdByHandle (repositoryId, handle, versionId = 'latest') {
    if (!(repositoryId && handle)) {
      return null
    }
    const psData = await dataFlowItemApi.getPsData(repositoryId)
    if (psData) {
      return axios.get(`/repositories/${repositoryId}/versions/${versionId}/references`, {
        params: {
          sourceDocumentId: psData.id,
          handle: handle
        }
      }).then(({ data }) => {
        return data.length > 0 ? data[0].targetItemId : null
      })
    } else {
      return null
    }
  },

  async getNumberingByItemIds (repositoryId, itemIds, versionId = 'latest') {
    return axios.post(`/repositories/${repositoryId}/versions/${versionId}/items/batchProperties`, {
      itemIds: JSON.stringify(itemIds)
    }).then(({ data }) => {
      const result = {}
      itemIds.forEach(element => {
        if (data[element]) {
          const numbering = data[element].find(pro => pro.name === 'numbering')
          if (numbering) {
            result[element] = numbering.value
          }
        }
      })
      return result
    })
  },

  /**
   * 获取item
   * @param repositoryId
   * @param itemId
   * @returns {Promise<{
   *   "repositoryId": "ec673a8a-f962-411c-bc34-f0c94e6f175a",
   *   "id": "f80c7dac-53e9-49ca-9cf0-fe33c2939def",
   *   "categoryName": "管子"
   * }>}
   */
  async getItem (repositoryId, itemId) {
    return axios.get(`/repositories/${repositoryId}/items/${itemId}`)
      .then(response => {
        return response.data
      }).catch(err => {
        ErrorHandler.formatError(err)
      })
  },

  /**
   * 获取item, 当未找到item时静默处理返回null
   * @param repositoryId
   * @param itemId
   * @returns {Promise<{
   *   "repositoryId": "ec673a8a-f962-411c-bc34-f0c94e6f175a",
   *   "id": "f80c7dac-53e9-49ca-9cf0-fe33c2939def",
   *   "categoryName": "管子"
   * }>}
   */
  async getItemQuietly (repositoryId, itemId) {
    return axios.get(`/repositories/${repositoryId}/items/${itemId}`)
      .then(response => {
        return response.data
      }).catch(e => {
        return null
      })
  },

  /**
   * 获取item
   * @param repositoryId
   * @param itemId
   * @returns {Promise<[{
      "name": "气压",
      "propertyValue": {
          "min": "5.15",          // 属性值的下限
          "max": "10"             // 属性值的上限
      },
      "unit": "Kpa"
    }, {
        "name": "物流代号",
        "propertyValue": {
            "value": "DA401"
        }
    }]>}
   */
  async getItemProperties (repositoryId, itemId, version = 'latest') {
    if (!(repositoryId && itemId)) {
      return []
    }
    return axios.get(`/repositories/${repositoryId}/versions/${version}/items/${itemId}/properties`, {
    }).then(response => {
      return response.data
    }).catch(err => {
      ErrorHandler.formatError(err)
    })
  },

  async getPIDProperties (repositoryId, docId, version = 'latest') {
    return axios.get(`/repositories/${repositoryId}/versions/${version}/documents/${docId}`, {
    }).then(response => {
      return response.data
    }).catch(err => {
      ErrorHandler.formatError(err)
    })
  },

  /**
   * 获取item所在的PID
   * @param repoId
   * @param itemId
   * @returns {Promise<AimsDocument> || null}
   */
  async getPIDs (repoId, itemId) {
    if (!(repoId && itemId)) {
      return null
    }

    return axiosEDC.get(`/repositories/${repoId}/pids`, {
      params: {
        itemId
      }
    }).then(({ data }) => {
      return data
    }).catch(err => {
      ErrorHandler.formatError(err)
    })
  },

  /**
   * 获取连接符的itemId连接的连接符的itemId和document
   * @param repoId
   * @param itemId
   * @returns {Promise<AimsDocument> || null}
   */
  async getConnectiveConnector (repoId, itemId) {
    if (!(repoId && itemId)) {
      return null
    }

    return axiosEDC.get(`/repositories/${repoId}/connector/document`, {
      params: {
        itemId
      }
    }).then(({ data }) => {
      return data
    }).catch(err => {
      ErrorHandler.formatError(err)
    })
  },

  /**
   * 根据handle获取itemId
   * @param repositoryId
   * @param sourceDocumentId
   * @param handle
   * @returns {Promise<null|itemId>}
   */
  async getItemIdBySourceDocIdAndHandleId (repositoryId, sourceDocumentId, handle) {
    if (!(repositoryId && handle && sourceDocumentId)) {
      return null
    }
    return axios.get(`/repositories/${repositoryId}/references`, {
      params: {
        sourceDocumentId: sourceDocumentId,
        handle: handle
      }
    }).then(({ data }) => {
      return data.length > 0 ? data[0].targetItemId : null
    })
  },
  /**
   * 获取节点的item
   * @param repositoryId
   * @param structureNodeId
   * @returns {Promise<unknown | void>}
   */
  getItemsOfStructureNode (repositoryId, structureNodeId, versionId = 'latest') {
    return axios.get(`/repositories/${repositoryId}/versions/${versionId}/structures/structureNodes/${structureNodeId}/items`)
      .then(({ data }) => Promise.resolve(data))
      .catch(err => ErrorHandler.formatError(err))
  },
  /**
   * 获取item的handle
   * @param repositoryId
   * @param documentId
   * @param itemIds
   * @returns {Promise<unknown | void>}
   */
  getHandlesOfItems (repositoryId, documentId, itemIds, versionId = 'latest') {
    return axios.post(`/repositories/${repositoryId}/versions/${versionId}/references/bulk`, {
      sourceDocumentId: documentId,
      targetItemIds: JSON.stringify(itemIds)
    }, {}).then(({ data }) => Promise.resolve(data))
      .catch(err => ErrorHandler.formatError(err))
  }
}
