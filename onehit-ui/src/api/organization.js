import { createAxios } from '@/common/axios'
import baseUrls from './baseUrlConfig'

const axios = createAxios(baseUrls.uams)

export const companyOrganizationApi = {
  /**
   * 获取部门
   * @param keyWord
   **/
  loadDepartments (keyWord) {
    return axios.get('/dept/manager/departments', { params: { keyWord }, cancelKey: 'cancelKey' })
  },
  /**
   * 删除部门
   * @param departmentId
   * @returns {Promise<AxiosResponse<any>>}
   */
  deleteDepartment (departmentId) {
    return axios.delete(`/dept/manager/departments/${departmentId}`)
  },
  /**
   * 新增部门
   * @param projectSpaceId
   * @param plantObjectId
   * @returns {Promise<AxiosResponse<any>>}
   */
  addDepartment (body) {
    return axios.post('/dept/manager/departments', body)
  },
  /**
   * 获取属性/文档交付进度数据
   * @param body { pageNum 页码,pageSize 每页显示条数,...filterAndValue 查找条件 }   * @returns {Promise<AxiosResponse<any>>}
   */
  updateDepartment (departmentId, body) {
    return axios.put(`/dept/manager/departments/${departmentId}`, body)
  },
  /**
   * 获取用户
   * @param data
   * @returns {Promise<AxiosResponse<any>>}
   */
  getUser (data) {
    return axios.post('/user/manager/users/internal', data, { isFormData: false, cancelKey: 'cancelKey' })
  },
  /**
   * 获取公司管理员
   * @returns {Promise<AxiosResponse<any>>}
   */
  getManagers () {
    return axios.get('/company/managers')
  },
  /**
   * 设置公司管理员
   * @param data
   * @returns {Promise<AxiosResponse<any>>}
   */
  setAdmin (data) {
    return axios.put('/company/managers', data, { isFormData: false })
  }
}
export const collaborativeCompanyApi = {
  getTypes () {
    return axios.get('/cooperateCompany/types')
  },
  /**
   * 获取协同公司
   * @param keyWord
   **/
  loadCompanies (data) {
    return axios.post('/cooperateCompanies/filter', data, { isFormData: false, cancelKey: 'cancelKey' })
  },
  /**
   * 删除协同公司
   * @param data
   * @returns {Promise<AxiosResponse<any>>}
   */
  deleteCompanies (data) {
    return axios.delete('/cooperateCompanies', { data }, { isFormData: false })
  },
  /**
   * 新增协同公司
   * @param projectSpaceId
   * @param plantObjectId
   * @returns {Promise<AxiosResponse<any>>}
   */
  addCompany (body) {
    return axios.post('/cooperateCompanies', body)
  },
  /**
   * 编辑协同公司
   * @param body { pageNum 页码,pageSize 每页显示条数,...filterAndValue 查找条件 }   * @returns {Promise<AxiosResponse<any>>}
   */
  updateCompany (cooperateCompanyId, body) {
    return axios.put(`/cooperateCompanies/${cooperateCompanyId}`, body)
  },
  /**
   * 协同公司是否存在
   * @param params
   * @returns {Promise<AxiosResponse<any>>}
   */
  existCompany (params) {
    return axios.get('/cooperateCompanies/exist', { params })
  },
  /**
   * 是否存在用户是申请人，且存在审批中或被驳回状态的申请单
   * @param data
   */
  existUsers (cooperateCompanyIds) {
    return axios.post('/cooperateCompanies/users', cooperateCompanyIds, { isFormData: false })
  },
  /**
   * 公司编码是否存在
   * @param params
   * @returns {Promise<AxiosResponse<any>>}
   */
  existCompanyCode (params) {
    return axios.get('/cooperateCompanies/exist/companyCode', { params })
  }
}
