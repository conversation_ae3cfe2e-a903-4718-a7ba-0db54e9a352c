import { createAxios } from "@/common/axios"
import baseUrls from './baseUrlConfig'

const axios = createAxios(baseUrls.onehit)
const scenatorAxios = createAxios(baseUrls.scenator)
const settingAxios = createAxios(baseUrls.setting)

export const puginApi = {
    getInternalIcons: () => axios.get(`/plugin/internal/icon`),
    createCustomPlugin: (params) => axios.post(`/plugin`, params),
    saveCustomPlugin: (id, params) => axios.post(`/scene/${id}`, params),
    getScene: (sceneId) => axios.get(`/plugin/scene/${sceneId}`),
    getPlugin: (sceneId) => axios.get(`/plugin/${sceneId}`),
    saveDefaultScene: (params) => axios.post(`/scene/default`, params, {
        headers: {'Content-Type': 'application/json'}
    }),
    getDefaultScene: () => axios.get(`/scene/default`),
}

export const viewDataApi = {
    getViewData: (sceneId, params) => axios.get(`/common/view/data/${sceneId}`, { params: params }),
    setViewData: (sceneId, params) => axios.post(`/common/view/data/${sceneId}`, params),
    deleteViewData: (sceneId, params) => axios.delete(`/common/view/data/${sceneId}`, { params: params })
}

export const clickLogApi = {
    addClickLog: (params) => axios.post(`/clickLog`, params),
    getVisitLog: (params) => axios.get('/clickLog', { params: params }),
    exportVisitLog: (params) => axios({
        url: '/clickLog/export',
        method: 'GET',
        params: params,
        responseType: 'blob'
    }),
    getVisitedPer: (params) => axios.get('/clickLog/statistics', { params: params }),
    updateClickLogApi: (id) => axios.post(`/clickLog/duration/${id}`)
}

export const labelApi = {
    // 获取标注类型列表
    getLabelTypeList: (params) => axios.get('/label/classification/list', {
        params: params
    }),
    // 创建标注类型
    addLabelType: (params) => axios.post('/label/classification/add', params, { isFormData: false }),
    // 编辑标注类型
    editLabelType: (params) => axios.put('/label/classification/edit', params, { isFormData: false }),
    // 删除标注类型
    deleteLabelType: (labelId) => axios.delete(`/label/classification/delete?id=${labelId}`),
    // 通过ID获取标注分类
    getLabelTypeById: (param) => axios.get(`/label/classification/queryById?id=${param.id}&queryChild=${param.queryChild}`),
    // 图例上传
    uploadImg: (param) => {
        const formData = new FormData()
        formData.append('file', param.file.raw)
        return axios.post(`/label/common/symbol/upload?type=${param.type}`, formData)
    }
}

export const configApi = {
    getSystemManageUrl: () => axios.get('/common/config/systemManage'),
    getAllScene: () => axios.get('/scene/allSceneName'),
    getSceneAuth: () => axios.get('/scene/auth'),
    getPermissions: (userRoleId) => axios.get(`/sceneAuth/getSceneAuth/${userRoleId}`),
    saveSceneAuth: (params) => axios.post('/sceneAuth/saveSceneAuth', params, {
        isFormData: false
    }),
    getWorkbenchUrl: () => axios.get('/common/config/url')
}

export const settingApi = {
    getSetting: () => settingAxios.get('/system/setting'),
    save: (data) => settingAxios.post("/system/setting", data, {
        isFormData: false
    }),
    delete: (id) => settingAxios.delete(`/system/setting/${id}`),
    update: (data) => settingAxios.post("/system/setting/update", data, {
        isFormData: false
    }),
}

export const brsIpConfigApi = {
    getConfigs: () => scenatorAxios.get('/ipConfig/getConfigs'),
    save: (data) => scenatorAxios.post("/ipConfig/save", data, {
        isFormData: false
    }),
    delete: (id) => scenatorAxios.delete(`/ipConfig/deleteConfig/${id}`),
    update: (id, data) => scenatorAxios.put(`/ipConfig/updateConfig/${id}`, data, {
        isFormData: false
    }),
}