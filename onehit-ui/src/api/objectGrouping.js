import { createAxios } from '@/common/axios'
import baseUrls from './baseUrlConfig'

const axios = createAxios(baseUrls.edc)
export const objectGroupingApi = {

  getALL () {
    return axios.get('/objectGroupingSeting')
  },

  getByKeyWord (keyWord) {
    return axios.get('/objectGroupingSeting/search', { params: { keyWord } })
  },

  update (id, body) {
    return axios.put(`/objectGroupingSeting/${id}`, body, { isFormData: false })
  }
}
