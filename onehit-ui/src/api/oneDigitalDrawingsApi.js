import  {createAxios} from "@/common/axios";
import baseUrls from "@/api/baseUrlConfig";

const axiosEDC = createAxios(baseUrls.edc)

export const oneDigitalDrawingsApi = {

  /**
   * 获取指定图纸ID对应的图纸信息
   * @param projectSpaceId
   * @param id
   * @returns {Promise<AxiosResponse<any>>}
   */
  getPidParseInfo (projectSpaceId, id) {
    return axiosEDC.get(`/projectSpaces/${projectSpaceId}/pids/${id}/drawings`)
  },

  /**
   * 获取模型列表
   * @param projectSpaceId
   * @param data
   * @returns {Promise<AxiosResponse<any>>}
   */
  getPidsModels (data) {
    return axiosEDC.post('/projectSpaces/pids/repos', data, {
      isFormData: false,
      cancelKey: 'cancelKey'
    })
  },

  getPidsHistoryByName (repoId, pidName) {
    return axiosEDC.get(`/projectSpaces/pids/history/repos/${repoId}`, {
      params: {
        pidName: pidName
      }
    })
  },

  getSpaceIdByRepoId (repoId) {
    return axiosEDC.post('/projectSpaces/psIds/repos',
      [repoId], {
        isFormData: false
      })
  },
  /**
   * 获取已启用的图纸属性
   * @returns {Promise<AxiosResponse<any>>}
   */
  getPropResource () {
    return axiosEDC.get('/pid/properties/enable')
  },

  /**
   * 查询m2d相关信息
   * @returns {Promise<AxiosResponse<any>>}
   */
  getM2DInfo () {
    return axiosEDC.get('/config/m2d/config')
  },

  /**
   * 获取图纸文件对应的m2d的图纸信息
   * @param projectSpaceId
   * @param id
   * @returns {Promise<AxiosResponse<any>>}
   */
  getPIdToM2dDrawings (projectSpaceId, id) {
    return axiosEDC.get(`/projectSpaces/${projectSpaceId}/pids/${id}/drawings/m2d`)
  }
}
