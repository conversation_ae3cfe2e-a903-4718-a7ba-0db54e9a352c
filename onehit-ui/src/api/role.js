import { createAxios } from '@/common/axios'
import baseUrls from './baseUrlConfig'

const axios = createAxios(baseUrls.edc)

export const roleManagementApi = {
  /**
   * 获取角色列表
   * @param keyWord
   **/
  loadRoles (keyWord) {
    return axios.get('/roles', { params: { keyWord }, cancelKey: 'cancelKey' })
  },
  /**
   * 删除角色
   * @param roleId
   * @returns {Promise<AxiosResponse<any>>}
   */
  deleteRole (roleId) {
    return axios.delete(`/roles/${roleId}`)
  },
  /**
   * 新增角色
   * @param body
   * @returns {Promise<AxiosResponse<any>>}
   */
  addRole (body) {
    return axios.post('/roles', body, { isFormData: false })
  },
  /**
   * 编辑角色
   * @param body
   * @param roleId
   * @returns {Promise<AxiosResponse<any>>}
   */
  updateRole (roleId, body) {
    return axios.put(`/roles/${roleId}`, body, { isFormData: false })
  },
  /**
   * 获取角色详情
   * @param data
   * @returns {Promise<AxiosResponse<any>>}
   */
  getRolesInfo (roleId) {
    return axios.get(`/roles/${roleId}`)
  },
  /**
   * 获取菜单列表
   * @returns {Promise<AxiosResponse<any>>}
   */getMenu () {
    return axios.get('/features')
  }
}
