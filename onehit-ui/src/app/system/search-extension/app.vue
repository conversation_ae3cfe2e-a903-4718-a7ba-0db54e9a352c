<!--
 * @Descriptin:
 * @Version: 0.1
 * @Autor: <PERSON>
 * @Date: 2022-04-12 15:28:09
 * @LastEditors: yangkai
 * @LastEditTime: 2023-01-29 11:17:43
-->
<template>
	<div id="search-app">
	<div class="searchdialog_wraper">
		<el-dialog
			class="scenatorStyle"
			custom-class="searcher_dialog"
			:title="$t('搜索')"
			:append-to-body="false"
			:visible.sync="visible"
			width="1120px"
			top=""
		>
			<div slot="title" class="search-dialog-header">
				<SearchInput slot="title" :searchModel="searchModel"></SearchInput>
			</div>
			<div class="searcher_content">
				<el-tabs class="scenatorStyle" v-model="activeIndex" type="card" @tab-click="handleSelect">
					<el-tab-pane v-for="(item, idx) in menuList" :key="idx" :label="$t(item.title)" :name="item.name">
						<keep-alive>
							<component
								:is="currentCard"
                :searchModel="searchModel"
								v-if="currentCard === item.name"
								@changeTab="allResut_changeTab"
							></component>
						</keep-alive>
					</el-tab-pane>
				</el-tabs>
			</div>
		</el-dialog>
	</div>
	</div>
</template>

<script>
import SearchInput from '@/components/search/searchInput/Index.vue';
import AllResult from '@/components/search/allResult/Index.vue';
import FactoryObject from '@/components/search/factoryObject/Index.vue';
import PidObject from '@/components/search/pidObject/Index.vue';
import DocObject from '@/components/search/docObject/Index.vue';
import {SearchModel} from "@/app/system/search-extension/model/SearchModel";
import bus from '@/utils/bus';
import {globalState} from "finger";
export default {
	name:"SearchApp",
	components: {
		SearchInput,
		AllResult,
		FactoryObject,
		PidObject,
		DocObject,
	},
	data() {
		return {
			visible:false,
			tableData: [],
			activeIndex: 'AllResult',
			currentCard: 'AllResult',
			menuList: [
				{
					title: '全部',
					name: 'AllResult',
				},
				{
					title: '对象',
					name: 'FactoryObject',
				},
				{
					title: '数字图纸',
					name: 'PidObject',
				},
				{
					title: '文档',
					name: 'DocObject',
				},
			],
        searchModel: new SearchModel()
		};
	},
  mounted() {
    bus.$on('searcherVmDialodShow', () => {
      this.visible = true;
      // 在这里初始化信息
      this.searchModel.init();
      this.searchModel.repoUtil.checkPermission();
    });
    bus.$on('searcherVmDialodHide', () => {
      this.visible = false;
    });
    globalState && globalState.watch('repoModelAmount', (preModel, currentModel, options) => {
      this.activeIndex = 'AllResult';
      this.currentCard = 'AllResult';
    });
  },
  methods: {
		handleSelect(e) {
			this.currentCard = this.activeIndex;
		},
		allResut_changeTab(name) {
			this.activeIndex = name;
			this.currentCard = name;
		},
	},
};
</script>

<style lang="less">
#search-app {
	font-family: Avenir, Helvetica, Arial, sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	width: 640px;
	height: 431px;
	/deep/.el-loading-mask {
		width: calc(100% - 1px);
		height: calc(100% - 41px);
		top: auto;
		bottom: 1px;
		.el-loading-spinner {
			position: relative;
			margin-top: 0;
			.circular {
				display: none;
			}
			&::after {
				position: absolute;
				content: '';
				display: inline-block;
				width: 6px;
				height: 6px;
				margin-left: 8px;
				border-radius: 50%;
				box-shadow: -30px 0px #0854a1, -10px 0px #0854a1, 10px 0px #0854a1;
				animation: shadowScaleForLoading 1.2s linear infinite;
			}
			@keyframes shadowScaleForLoading {
				15% {
					box-shadow: -30px 0px 0px 4px #0854a1, -10px 0px #0854a1, 10px 0px #0854a1;
				}

				33% {
					box-shadow: -30px 0px #0854a1, -10px 0px 0px 4px #0854a1, 10px 0px #0854a1;
				}

				66% {
					box-shadow: -30px 0px #0854a1, -10px 0px #0854a1, 10px 0px 0px 4px #0854a1;
				}

				100% {
					box-shadow: -30px 0px #0854a1, -10px 0px #0854a1, 10px 0px #0854a1;
				}
			}
		}
	}
}
.searcher_dialog {
	height: 765px;
	.el-dialog__header {
		display: flex;
		align-items: center;
		padding: 0 16px 0 8px !important;
		#searcher_input {
			height: 32px;
			input {
				border: none;
			}
		}
		.el-dialog__headerbtn {
			width: 20px;
			height: 20px;
			border: 1px solid transparent;
			border-radius: 2px;
			&:hover {
				background: #ebf5fe;
				border: 1px solid #0854a1;
			}
			&:active {
				background: #0854a1;
				border-radius: 2px;
				i:before {
          font-family: onehit-icon;
          content: "\e65a";
          color: #FFFFFF;
          font-size: 20px;
				}
			}
			i {
				width: 20px !important;
				height: 20px !important;
				display: inline-block !important;
				margin-top: 0 !important;
				cursor: pointer !important;
        &:before {
          font-family: onehit-icon;
          content: "\e65a" !important;
          color: #0854A1;
          font-size: 20px;
        }
			}
		}
	}
	.el-dialog__header:hover {
		box-shadow: 0px 0px 4px 0px rgba(0,0,0,0.15), inset 0px -1px 0px 0px #0854A1;
	}
	.el-dialog__body {
		height: calc(100% - 44px);
		padding: 0;
	}
	.el-tabs {
		width: 100%;
		height: 100%;
		.el-tabs__header {
			height: 56px;
			line-height: 56px;
			padding: 4px 24px 0 24px;
			box-shadow: none;
			.el-tabs__nav-wrap {
				box-shadow: inset 0px -1px 0px 0px #d9d9d9;
			}
		}
		.el-tabs__content {
			height: calc(100% - 90px);
			.el-tab-pane {
				height: 100%;
			}
		}
		.el-tabs__nav-scroll {
			display: flex;
			flex-direction: row;
			justify-content: left;
		}
	}
	.el-tabs__item {
		width: auto !important;
	}
	.el-tabs__nav-scroll {
		display: flex !important;
		justify-content: center;
	}
	.el-dialog__close {
		display: none !important;
	}
}
.searcher_content {
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	.el-tabs--card {
		.el-icon-arrow-left {
			background: none !important;
			&:hover {
				border: none;
			}
		}
	}
	.el-tabs--card {
		.el-icon-arrow-right {
			background: none !important;
			&:hover {
				border: none;
			}
		}
	}
	/*.el-pagination {
		height: auto;
	}*/
}
</style>
