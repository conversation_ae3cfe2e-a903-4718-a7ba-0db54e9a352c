/*
 * @Descriptin:
 * @Version: 0.1
 * @Autor: <PERSON>
 * @Date: 2022-07-14 17:17:55
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-07-14 17:18:04
 */
class Bus {
	constructor() {
		this.callbacks = {};
	}
	$on(name, fn) {
		this.callbacks[name] = this.callbacks[name] || [];
		this.callbacks[name].push(fn);
	}
	$emit(name, args) {
		if (this.callbacks[name]) {
			this.callbacks[name].forEach((cb) => cb(args));
		}
	}
}

export default Bus;
