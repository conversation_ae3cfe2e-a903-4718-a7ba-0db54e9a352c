import * as condicio from "condicio";

/**
 * @ignore
 * 检索：获取排序
 */
class SearchSorter {

	/**
	 *
	 * @param {Array} sorters
	 * @param {String} sorter.sortName   排序的属性名
	 * @param {String} sorter.sortOrder  排序的方式 （desc、asc）
	 * @param {String} sorter.sortType   排序类型   匹配度：score   属性：property
	 */
	constructor(sorters) {
		condicio.checkIsArray(sorters, "sorters is must be 'Array'");
		this.sorterType = "SearchSorter";
		this.sorters = sorters;
	}

	getType() {
		return this.sorterType;
	}

	getSorters() {
		const sorters = [];
		for (let index =0; index < this.sorters.length; index ++) {
			const s = this.sorters[index];

			condicio.checkIsString(s.sortName, "sortName must be a 'string'");
			condicio.checkIsString(s.sortOrder, "sortOrder must be a 'string'");
			condicio.checkIsString(s.sortType, "sortType must be a 'string'");

			const sorter = {};
			let sortName = "";
			if (s.sortType === "property") {
				sortName = `properties.${s.sortName}.value.keyword`;
			} else if (s.sortType === "score") {
				sortName = s.sortName;
			} else {
				throw Error("sortType is illegal");
			}
			sorter[sortName] = {};
			sorter[sortName]["order"] = s.sortOrder;
			sorters.push(sorter);
		}
		return sorters;
	}
}
export default SearchSorter;