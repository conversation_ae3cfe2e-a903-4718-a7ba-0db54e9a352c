import {createFilter} from "./Filter/FilterFactory";
import {createSorter} from "./Sorter/SorterFactory";
/**
 * 查询工厂对象
 *
 * @param {Object}     options
 * @param {Object}     options.size            一次查询多少条
 * @param {Object}     options.from            查询起始位置
 * @param {String[]}   options.categories      对象分类
 * @param {String[]}   options.targetProNames   检索目标属性名称  如：位号
 * @param {String}     options.targetProValue  检索目标属性值
 * @param {Array}   options.properties         属性筛选条件集合
 *
 * @param {Object}  property                   属性筛选条件
 * @param {String}  property.name         属性名称
 * @param {String}  property.value        属性值
 * @param {boolean} property.range        标识区间查询
 * @param {String}  property.symbol       符号  例： ‘>=’ ‘<=’
 *
 * @param {Array}     options.sorters    排序筛选
 * @param {String} sorter.sortName   排序的属性名
 * @param {String} sorter.sortOrder  排序的方式 （desc、asc）
 *
 * @return {Object} 查询语句
 */
 export const getQueryString =(options) => {
    const targetfilter = createFilter({
        type : "SearchTargetsProFilter",
        targetProNames : options.targetProNames,
        targetProValue : options.targetProValue
    });
    const targetsfilter = targetfilter.getFilters();
    const propertiesFilter = createFilter({type : "SearcherPropertyFilter", properties : options.properties});
    const filters = propertiesFilter.getFilters();

    if (options.categories.length > 0) {
        const categoriesFilter = createFilter({type : "SearchCategoriesFilter", categories : options.categories});
        filters.push(categoriesFilter.getFilter());
    }

    const sorter = createSorter({type : "SearchSorter", sorters : options.sorters});
    const searchSorter = sorter.getSorters();

    const searchCondition = {
        query : {
            bool : {
                must : [{
                    bool : {
                        minimum_should_match : 1,
                        should : targetsfilter
                    }
                }]
            }
        },
        size : options.size,
        from : options.from
    };
    if (filters.length > 0) {
        searchCondition.query.bool.filter = filters;
    }
    if (searchSorter.length > 0) {
        searchCondition.sort = searchSorter;
    }
    return searchCondition;
};


