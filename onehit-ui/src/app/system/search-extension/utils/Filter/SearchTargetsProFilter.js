import _ from "lodash-oneHit";

/**
 * @ignore
 * 检索：获取关键字筛选条件
 */
class SearchTargetsProFilter {

	/**
	 *
	 * @param {Array} targetProNames  目标属性集合
	 * @param targetProValue          目标属性值
	 */
	constructor(targetProNames, targetProValue) {
		if (!_.isArray(targetProNames)) {
			throw Error("targetProNames must be array");
		}
		if (_.isNull(targetProValue) || _.isUndefined(targetProValue)) {
			throw Error("targetProValue cannot be empty");
		}

		this.filterType = "SearchTargetsProFilter";
		this.targetProNames = targetProNames;
		this.targetProValue = targetProValue;
	}

	getType() {
		return this.filterType;
	}

	/**
	 *
	 * @return {Array}
	 */
	getFilters () {
		const filters = [];
		const that = this;
		this.targetProNames.forEach(function (t) {
			const match = {};
			match.match_phrase = {};
			const propertyName = `${t}.value`;
			match.match_phrase[propertyName] = that.targetProValue;
			filters.push(match);
		});
		return filters;
	}
}
export default SearchTargetsProFilter;