import _ from "lodash-oneHit";

/**
 * @ignore
 * 检索：获取分类条件筛选
 */
class SearchCategoriesFilter{
	/**
	 *
	 * @param {Array} categories
	 */
	constructor(categories) {
		if (!_.isArray(categories)) {
			throw Error("categories must be array");
		}

		this.filterType = "SearchCategoriesFilter";
		this.categories = categories;
	}

	getType() {
		return this.filterType;
	}

	/**
	 *
	 * @return {{terms: {"categories.keyword": *}}}
	 */
	getFilter() {
		const filter = {
			"terms" : {
				"categories.keyword" : this.categories
			}
		};
		return filter;
	}
}
export default SearchCategoriesFilter;