import * as condicio from "condicio";
import _ from "lodash-oneHit";

/**
 * @ignore
 * 检索：获取属性筛选条件
 */
class SearchPropertiesFilter {
	/**
	 *
	 * @param {Array}   properties 属性筛选条件
	 *
	 * @param {String}  properties.name         属性名称
	 * @param {String}  properties.value        属性值
	 * @param {boolean} properties.range        标识区间查询
	 * @param {String}  properties.symbol       符号  例： ‘>=’ ‘<=’
	 */
	constructor(properties) {
		condicio.checkIsArray(properties, "properties must be array");
		this.filterType = "SearcherPropertyFilter";
		this.properties = properties;
	}

	getType() {
		return this.filterType;
	}

	/**
	 *
	 * @return {Array}
	 */
	getFilters() {
		const filters = [];
		this.properties.forEach(function (p) {
			condicio.checkIsString(p.name, "name must be a 'string'");
			condicio.checkIsBoolean(p.range, "range must be a 'boolean'");
			condicio.checkNotNull(p.value, "value cannot be empty");
			condicio.checkNotUndefined(p.value, "value cannot be empty");
			if (p.range && (_.isUndefined(p.symbol) || _.isNull(p.symbol))) {
				throw Error("symbol cannot be empty");
			}
			const filter = {};
			let propertyName = `properties.${p.name}.value.keyword`;
			if (p.range) {
				filter.range = {};
				let symbol;
				if (">=" === p.symbol) {
					symbol = "gte";
				}
				else if ("<=" === p.symbol) {
					symbol = "lte";
				}
				else if ("<" === p.symbol) {
					symbol = "lt";
				}
				else if (">" === p.symbol) {
					symbol = "gt";
				}
				else {
					throw Error("This symbol is not supported!");
				}
				propertyName = `properties.${p.name}.value`;
				filter.range[propertyName] = {};
				filter.range[propertyName][symbol] = p.value;
			} else {
				filter.term = {};
				filter.term[propertyName] = {
					value : p.value
				};
			}
			filters.push(filter);
		});
		return filters;
	}
}

export default SearchPropertiesFilter;