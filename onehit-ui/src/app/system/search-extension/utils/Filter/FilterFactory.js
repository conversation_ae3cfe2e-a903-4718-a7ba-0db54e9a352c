import SearchPropertiesFilter from "./SearchPropertiesFilter";
import SearchCategoriesFilter from "./SearchCategoriesFilter";
import SearchTargetsProFilter from "./SearchTargetsProFilter";

export function createFilter(filterInfo) {
	if (filterInfo.type === "SearcherPropertyFilter") {
		return new SearchPropertiesFilter(filterInfo.properties);
	}
	else if (filterInfo.type === "SearchCategoriesFilter") {
		return new SearchCategoriesFilter(filterInfo.categories);
	}
	else if (filterInfo.type === "SearchTargetsProFilter") {
		return new SearchTargetsProFilter(filterInfo.targetProNames, filterInfo.targetProValue);
	}
	else {
		throw Error("filter type is illegal");
	}
}