/*
 * @Descriptin:
 * @Version: 0.1
 * @Autor: <PERSON>
 * @Date: 2022-04-27 11:30:05
 * @LastEditors: --
 * @LastEditTime: 2023-06-19 10:41:33
 */
import _ from 'lodash-oneHit';

// TODO 后期废弃掉
class queryFilterString {
	constructor() {
		this.filter = [];
		this.previousName = null;
	}

	setFilter(properties) {
		let termsList = [];
		let bool;
		for (let index = 0; index < properties.length; index++) {
			const element = properties[index];
			let propertyName = `properties.${element.name}.value`;
			let operator;
			try {
				if (element.type === 'number' && element.symbol !== '=') {
					let range;
					if (!range) {
						range = {};
					}
					if (!range[propertyName]) {
						range[propertyName] = {};
					}
					operator = this.getOperator(element.symbol);
					range[propertyName][operator] = element.value;
					// 根据属性的集合类型进行不同的封装
					if (element.collectionType === 'union') {
						if (!bool || this.previousName !== element.name) {
							bool = {};
							bool.should = [];
						}
						bool.should.push({
							range,
						});
						if (this.previousName === element.name) {
							this.filter.push({
								bool,
							});
						}
						this.previousName = element.name;
					} else {
						this.filter.push({
							range,
						});
					}
					if (element.unit !== null) {
						let terms;
						let propertyUnit = `properties.${element.name}.unit.keyword`;
						if (!terms) {
							terms = {};
						}
						if (!terms[propertyUnit]) {
							terms[propertyUnit] = [];
						}
						terms[propertyUnit].push(element.unit);
						this.filter.push({
							terms,
						});
					}
				} else if (element.type === 'string' || (element.type === 'number' && element.symbol === '=')) {
					let propertyTerms = _.find(termsList, {
						name: element.name,
					});
					if (propertyTerms && element.type === 'string') {
						propertyTerms.valueList.push(element.value);
					} else {
						let valueList = [];
						let filterTerms = [];
						if (element.type === 'number') {
							valueList.push(element.value);
							filterTerms = {
								valueList: valueList,
								name: element.name,
								unit: element.unit,
								type: element.type,
								collectionType: element.collectionType,
							};
						} else {
							valueList.push(element.value);
							filterTerms = {
								valueList: valueList,
								name: element.name,
								type: element.type,
							};
						}
						termsList.push(filterTerms);
						if (termsList.length > 0) {
							for (let index = 0; index < termsList.length; index++) {
								const termsElement = termsList[index];
								let terms, propertyName;
								if (termsList[index].type === 'string') {
									propertyName = `properties.${termsElement.name}.value.keyword`;
								} else {
									propertyName = `properties.${termsElement.name}.value`;
								}
								if (!terms) {
									terms = {};
								}
								if (!terms[propertyName]) {
									terms[propertyName] = [];
								}
								terms[propertyName] = termsElement.valueList;
								if (termsList[index].collectionType === 'union') {
									if (index === termsList.length - 1) {
										if (!bool || this.previousName !== termsList[index].name) {
											bool = {};
											bool.should = [];
										}
										bool.should.push({
											terms,
										});
										if (this.previousName === termsList[index].name) {
											this.filter.push({
												bool,
											});
										}
										this.previousName = termsList[index].name;
									}
								} else {
									this.filter.push({
										terms,
									});
								}
								if (termsList[index].type === 'number' && index === termsList.length - 1) {
									let terms = {};
									let propertyUnit = `properties.${termsElement.name}.unit.keyword`;
									if (!terms[propertyUnit]) {
										terms[propertyUnit] = [];
									}
									terms[propertyUnit].push(termsElement.unit);
									this.filter.push({
										terms,
									});
								}
							}
						}
					}
				}
			} catch (error) {
				console.log(error);
			}
		}
	}

	setBoolShouldFilter() {}
	/**
	 * return {
			"bool":{
				"should":[
					{
						"range":{
							"properties.保温厚度.value":{
								"lt":10
							}
						}
					},
					{
						"range":{
							"properties.保温厚度.value":{
								"gt":100
							}
						}
					}
				]
			}
		}
	 */
	getBoolShouldFilter() {}

	getFilter() {
		return this.filter;
	}

	getOperator(symbol) {
		let operator = null;
		if ('≥' === symbol) {
			operator = 'gte';
		} else if ('≤' === symbol) {
			operator = 'lte';
		} else if ('<' === symbol) {
			operator = 'lt';
		} else if ('>' === symbol) {
			operator = 'gt';
		} else {
			throw Error('This symbol is not supported!');
		}
		return operator;
	}
}

export default queryFilterString;
