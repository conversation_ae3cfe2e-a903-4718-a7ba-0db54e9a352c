import {store} from "@/store";
import {Api} from "@/api";
import {globalState} from "finger";
import _ from "lodash";
import bus from "@/utils/bus";
import FilterData from "@/app/system/search-extension/model/FilterData";
import {RepoUtil} from "@/app/system/search-extension/model/RepoUtil";
import {Message} from "element-ui";

/**
 * 2024.12.31重构
 * 超级检索的数据处理Model
 * 1、所有的数据处理逻辑都从这里来执行
 * // TODO
 * 2、厂区切换的逻辑没有测试过，可能会有问题
 */
export class SearchModel {
    constructor() {
        this.keyWord = null;
        this.searchConfigs = store.state.config.search;
        this.permission = {
            R3D: true,
            PID: true,
            DOC: true
        };
        this.isInit = false;

        this.allPidDocuments = [];
        this.callbacks = [];

        this.DEFAULT_SIZE = 9999;
        this.DEFAULT_BEGIN_INDEX = 0;

        this.factoryContentList = [];
        this.pidContentList = [];
        this.docContentList = [];

        this.factoryContentTotal = 0;

        this.filterData = new FilterData(this);
        this.repoUtil = new RepoUtil(this);

        this.documents = [];

        // this.init();
    }

    // 检索的数据
    setListener(callback){
        this.callbacks.push(callback);
    }

    publish(dataType, data, total){
        this.callbacks.forEach(callback => {
            callback(dataType, data, total)
        })
    }

    async search(keyword) {
        this.publish("startLoading", keyword);
        this.keyWord = keyword;
        if (keyword.trim() === ''){
            this.factoryContentList = [];
            this.pidContentList = [];
            this.docContentList = [];
            this.publish("searcherVmdocContentList", []);
            this.factoryContentTotal = 0;
            this.publish("searcherVmfactoryConentList", [], 0);
            this.publish("searcherVmpidContentList", []);
            return;
        }

        let _this = this;
        const globalData = (globalState && globalState.get('repoModelAmount')) || null;
        let config = this.searchConfigs.repositoriesConfig;
        const noScenePermission = _this.permission.R3D || _this.permission.PID || _this.permission.DOC;
        if (globalData && globalData.length > 0) {
            const repoIds = globalData.map((globalDatai) => {
                return globalDatai.id || '';
            });
            if (repoIds.length === 0 || !noScenePermission) {
                this.showNoRepositoryError();
                return;
            }
            if (_this.permission.R3D) {
                await _this.getDataModels(globalData);
                await _this.search_FactoryFromEs(globalData);
            }
            if (_this.permission.PID) {
                _this.getPidData(globalData, config, 'repoModelAmount');
            }
            if (_this.permission.DOC) {
                _this.get_DocumentsData(globalData);

            }
        } else if (globalData && globalData.length === 0) {
            _this.showNoRepositoryError();
        } else {
            const repoPermission = await _this.repoUtil.checkPermission(config)
            if ((repoPermission.factoryRepository.length === 0 && repoPermission.pidRepository.length === 0 && repoPermission.docRepository.length === 0) || !noScenePermission) {
                _this.showNoRepositoryError();
                return;
            }
            if (repoPermission.pidRepository.length > 0 && _this.permission.PID) {
                _this.getPidData(_this.repoUtil.allRepositories, config);
            }else {
                this.publish("searcherVmpidContentList", []);
            }
            if (repoPermission.docRepository.length > 0 && _this.permission.DOC) {
                _this.get_DocumentsData();
            }else {
                this.publish("searcherVmdocContentList", []);
            }
            if (repoPermission.factoryRepository.length > 0 && _this.permission.R3D) {
                await _this.getDataModels();
                await _this.search_FactoryFromEs(repoPermission.factoryRepository);
            }else {
                this.factoryContentTotal = 0;
                this.publish("searcherVmfactoryConentList", [], 0);
            }
        }
    }

    async init(){
        if (this.isInit){
            return;
        }
        const permissionScenes = await this.getUserScene();
        this.permission.R3D = permissionScenes.includes(this.searchConfigs.render3DName);
        this.permission.PID = permissionScenes.includes(this.searchConfigs.PidSceneName);
        this.permission.DOC = permissionScenes.includes(this.searchConfigs.docSceneName);
        this.isInit = true;
    }

    /**
     * @descripton: 获取document文档
     * @param {*}
     * @return {*}
     * @author: Gary
     */
    async get_DocumentsData(globalData) {
        // 创建多请求执行队列，在获取全部数据后作合并
        let keywords = (this.keyWord && [this.keyWord]) || [];
        let repoIds = [];
        if (globalData) {
            repoIds = globalData.map((globalDatai) => {
                return globalDatai.id || '';
            });
        } else {
            // 获取仓库id
            repoIds = this.repoUtil.docRepoModels.map((pry) => pry.id)
                .filter(pro_id => pro_id ? pro_id : false);
        }
        // 如果仓库数量为空，直接返回
        if (repoIds.length === 0) {
            this.docContentList = [];
            this.publish("searcherVmdocContentList", []);
        }
        try {
            Promise.all(
                repoIds.map(async (proId) => {
                    return Api.searchDocumentsByText({
                        repositoryId: proId,
                        keywords,
                    }).then(async (res) => {
                        let newList = res.data?.results.map((drwd) => {
                            drwd.repositoryId = proId;
                            drwd.name = this.replaceClassName(drwd.name.toString());
                            return drwd;
                        });
                        newList = await this.getDocumentProperty(newList);
                        return newList;
                    }).catch((error) => {
                            return [];
                        });
                }),
            ).then((result) => {
                const allResult = result.reduce((total, next) => {
                    return total.concat(next);
                }, []);
                const doclist = allResult.filter((ar) => {
                    if (!ar.categories.includes(this.searchConfigs.PIDDocmentCategory)) {
                        return ar;
                    }
                });
                this.docContentList = doclist;
                this.publish("searcherVmdocContentList", doclist);
            }).catch(() => {
                this.docContentList = [];
                this.publish("searcherVmdocContentList", []);
            });
        } catch (error) {
            console.log(error)
            this.docContentList = [];
            this.publish("searcherVmdocContentList", []);
        }
    }


    /**
     * 优化PID查询 使用ES查询
     */
    async getPidData(allRepositoriesInfo, config, type) {
        this.loading_pid = true;
        // 如果有公共厂区数据 使用厂区数据
        if (type === 'repoModelAmount') {
            const documentsResult = await this.getGlobalDataDocuments(allRepositoriesInfo);

            this.allPidDocuments = documentsResult;
            this.optimizeSearchPid(this.allPidDocuments);
        } else {
            // 如果已经请求过文档则直接使用
            if (this.allPidDocuments.length <= 0) {
                try {
                    const {data: documentResult} = await this.getAllPidDocuments();
                    const documents = documentResult.hits.hits.map((item) => {
                        return {
                            repositoryId: item._index.split('_')[0],
                            id: item._id,
                            name: item._source.name,
                            documentCategory: item._source.categories,
                            properties: item._source.properties,
                        };
                    });
                    this.allPidDocuments = documents;
                    this.optimizeSearchPid(this.allPidDocuments);
                } catch (error) {
                    console.log(error)
                    this.loading_pid = false;
                }
            } else {
                this.optimizeSearchPid(this.allPidDocuments);
            }
        }
    }

    /**
     * @descripton: 从es查询工厂对象
     * @param {*}
     * @return {*}
     * @author: Gary
     */
    search_FactoryFromEs(repositories) {
        let _this = this;
        _this.loading_factory = true;
        let repoIds = repositories.map((repository) => {
            return repository.id || '';
        });
        _this.dataModels
            .searchItems(repoIds, _this.queryString)
            .then(async function (res) {
                let result = res.hits.hits;
                let properties = result.map((hit) => {
                    let newObj = {};
                    // 使用属性生成工厂对象表格数据
                    hit._source.properties
                        ? Object.keys(hit._source.properties).forEach((prop) => {
                            newObj[prop] = hit._source.properties[prop].value;
                        })
                        : null;
                    // 将分类数据塞表格数据
                    hit._source.categories ? (newObj['分类'] = hit._source.categories.join('，')) : null;
                    if (Object.prototype.hasOwnProperty.call(newObj, '名称')) {
                        // 处理生成的表格数据，对检索词高亮
                        newObj['名称'] = _this.highlightText(newObj['名称']);
                    }
                    if (Object.prototype.hasOwnProperty.call(newObj, '位号')) {
                        // 处理生成的表格数据，对检索词高亮
                        newObj['位号'] = _this.highlightText(newObj['位号']);
                    }
                    newObj.itemId = hit._id;
                    newObj.repositoryId = hit._index.split('_')[0];
                    return newObj;
                });
                _this.factoryContentList = properties;
                _this.factoryContentTotal = res.hits.total;
                _this.publish("searcherVmfactoryConentList", properties, res.hits.total);
                // _this.$store.state.factoryContentTotal = res.hits.total;
            })
            .catch((error) => {
                console.log('error', error);
                _this.factoryContentList = [];
                _this.factoryContentTotal = 0;
                _this.publish("searcherVmfactoryConentList", [], 0);
            });
    }

    /**
     * @descripton: 获取查询工厂对象的查询条件
     * @param {*}
     * @return {*}
     * @author: Gary
     */
    async getDataModels(globalData) {
        this.dataModels = this.repoUtil.itemRepoModelsObj;
        if (globalData && globalData.length) {
            this.dataModels.repoModels = globalData;
        }
        this.getQueryStringOption();
    }

    // 获取当前用户场景权限
    async getUserScene() {
        const {data: sceneDatas} = await Api.getUserScene();

        const allScenes = [];
        sceneDatas.forEach(sceneData => {
            allScenes.push(sceneData.name);
        });
        return allScenes;
    }

    showNoRepositoryError() {
        Message({
            dangerouslyUseHTMLString: true,
            duration: 3000,
            customClass: 'scenatorStyle scenator_briefMsg error',
            message: `<div><article>没有仓库数据</article></div>`,
        });
        this.factoryContentList = [];
        this.pidContentList = [];
        this.docContentList = [];
        this.publish("searcherVmdocContentList", []);
        this.factoryContentTotal = 0;
        this.publish("searcherVmfactoryConentList", [], 0);
        this.publish("searcherVmpidContentList", []);
    }

    /**
     * @descripton: 对检索词进行高亮处理
     * @param {*} val 全文本
     * @return {*} 处理后的全文本
     * @author: Gary
     */
    highlightText(val) {
        let result = '';
        if (val === '' || !val) {
            return result;
        }
        const reg = new RegExp(this.keyWord, 'gi');
        result = val.replace(reg, function (txt) {
            return `<span style="color:#EA4436">${txt}</span>`;
        });
        return result;
    }

    // 名称高亮类名替换
    replaceClassName(oldStr) {
        const newStr = oldStr.replace(/rzon-highlight/g, 'rzon-highlight-name');
        return newStr;
    }

    async getDocumentProperty(docList){
        if (docList && docList.length > 0){
            try {
                const ids = [];
                docList.forEach(doc => {
                    ids.push(doc.id)
                });
                const result = await Api.getDocumentProperty(docList[0].repositoryId, ids);
                const hits = result.data.hits.hits;
                docList.forEach(doc => {
                    const hit = hits.find(hit => hit._id === doc.id);
                    if (hit._source.properties){
                        doc.properties = hit._source.properties;
                    }else {
                        doc.properties = {}
                    }
                })
                return docList;
            }catch (e) {
                console.log(e)
                return docList
            }
        }else {
            return docList;
        }
    }

    /**
     * @descripton: 组装查询条件
     * @author: Gary
     */
    getQueryStringOption() {
        const options = this.getSearchOption();
        options.targetProNames = this.searchConfigs.propertyNames;
        options.targetProValue = this.keyWord;
        this.queryString = this.dataModels.getQueryString(options);
    }

    /**
     * @descripton: 参数配置
     * @return {*} options
     * @author: Gary
     */
    getSearchOption() {
        const options = {
            size: 4,
            from: 0,
            categories: [],
            targetProNames: [],
            targetProValue: '',
            properties: [],
            sorters: [],
        };
        return options;
    }

    /**
     * 通过ES查PID的所有文档
     */
    async getAllPidDocuments() {
        let pidRepositoriesIdArr = [];
        this.repoUtil.pidRepoModels.forEach(item => {
            pidRepositoriesIdArr.push(item.id + '_v1' + '_document')
        })
        let url = pidRepositoriesIdArr.join(',');
        const filter = {
            query: {
                bool: {
                    must: [],
                    filter: {
                        terms: {
                            'categories.keyword': ['pidintellectdwg'],
                        },
                    },
                },
            },
            from: 0,
            size: 9999,
        };
        return Api.getEsDocuments(url, filter);
    }

    /**
     * 优化pid查询
     * @param {*} documents pid文档
     */
    async optimizeSearchPid(documents) {
        try {
            let repositoriesIds = [];
            let currentRepoModel = globalState.get('repoModelAmount');
            if (currentRepoModel && currentRepoModel.length > 0) {
                repositoriesIds = _.map(currentRepoModel, 'id');
            } else {
                repositoriesIds = _.map(this.repoUtil.pidRepoModels, 'id');
            }
            console.log('repositoriesIds', repositoriesIds);
            const pidSearchProperties = this.searchConfigs.PIDPropertyNames;
            let esOptions = {
                size: this.DEFAULT_SIZE,
                from: this.DEFAULT_BEGIN_INDEX,
                properties: [],
                categories: [],
                sorters: [],
                targetProNames: pidSearchProperties,
                targetProValue: this.keyWord,
            };

            const query = this.repoUtil.pidRepoModelsObj.getQueryString(esOptions);

            // 获取所有 "位号" 关键字的items
            const contentResult = await this.getEsDocByPropertiesMatchphrase(repositoriesIds, query);
            let itemIds = {};
            contentResult.forEach((item) => {
                if (!itemIds[item._index.split('_')[0]]) {
                    itemIds[item._index.split('_')[0]] = [];
                    itemIds[item._index.split('_')[0]].push(item._id);
                } else {
                    itemIds[item._index.split('_')[0]].push(item._id);
                }
            });

            let allProperties = [];
            const promises = [];
            for (const key in itemIds) {
                if (Object.hasOwnProperty.call(itemIds, key)) {
                    const element = itemIds[key];
                    itemIds[key] = Array.from(new Set(element));

                    const promise = (async () => {
                        try {
                            // 获取文档关联关系
                            const {data: allItemWithProperty} = await Api.getBulkReferences({
                                repositoryId: key,
                                targetItemIds: itemIds[key]
                            });
                            return allItemWithProperty;
                        } catch (error) {
                            console.log('error', error);
                            return [];
                        }
                    })();

                    promises.push(promise);
                }
            }
            try {
                const allItemWithProperties = await Promise.all(promises);
                allProperties = allProperties.concat(allItemWithProperties.flat());
            } catch (error) {
                console.log('error', error);
            }

            // 查找文档和itemId对应关系
            let bindDoProperties = [];
            const docMap = new Map();
            documents.forEach(_doc => {
                docMap.set(_doc.id, _doc);
            });

            allProperties.forEach(item => {
                const _doc = docMap.get(item.sourceDocumentId);
                if (_doc) {
                    bindDoProperties.push({
                        ...item,
                        repositoryId: _doc.repositoryId,
                    });
                }
            });

            // 批量获取items的属性信息
            let bindDoPropertiesObject = {};
            bindDoProperties.forEach((item) => {
                if (!bindDoPropertiesObject[item.repositoryId]) {
                    bindDoPropertiesObject[item.repositoryId] = [];
                    bindDoPropertiesObject[item.repositoryId].push(item);
                } else {
                    bindDoPropertiesObject[item.repositoryId].push(item);
                }
            });

            // 在绑定好的对象中拿itemID
            let allItemWithPropertyForEs = [];
            let docObject = {};
            for (const key in bindDoPropertiesObject) {
                if (Object.hasOwnProperty.call(bindDoPropertiesObject, key)) {
                    const element = bindDoPropertiesObject[key];
                    element.forEach((item) => {
                        if (!docObject[item.sourceDocumentId]) {
                            docObject[item.sourceDocumentId] = [];
                            docObject[item.sourceDocumentId].push({
                                repositoryId: key,
                                itemId: item.targetItemId,
                            });
                        } else {
                            docObject[item.sourceDocumentId].push({
                                repositoryId: key,
                                itemId: item.targetItemId,
                            });
                        }
                    });

                    let targetIds = element.map((item) => item.targetItemId);
                    // 通过itemId获取工厂对象 (一个item就是一个工厂对象)
                    let itemWithPropertyForEs = await this.getAllBatchPropertiesForEs(key, targetIds);
                    allItemWithPropertyForEs.push(itemWithPropertyForEs);
                }
            }


            let documentPropertysForEs = [];
            allItemWithPropertyForEs = _.flattenDeep(allItemWithPropertyForEs);
            allItemWithPropertyForEs.forEach((item) => {
                for (const docKey in docObject) {
                    if (Object.hasOwnProperty.call(docObject, docKey)) {
                        const docel = docObject[docKey];
                        docel &&
                        docel.forEach((doc) => {
                            if (doc.itemId === item.id) {
                                documentPropertysForEs.push({
                                    repositoryId: doc.repositoryId,
                                    itemId: doc.itemId,
                                    properties: item.properties,
                                    documentId: docKey,
                                });
                            }
                        });
                    }
                }
            });

            let obj = {},
                tableDataForPropertys = [];
            let copyDocuments = _.cloneDeep(documents);

            documentPropertysForEs.forEach((dpItem) => {
                let docIndex = copyDocuments.findIndex((d) => d.id === dpItem.documentId);
                if (docIndex !== -1) {
                    let docSource = copyDocuments[docIndex];
                    if (!obj[dpItem.documentId]) {
                        obj[dpItem.documentId] = 1;
                        const {itemIds, texts} = this.findProperties(dpItem.documentId, documentPropertysForEs);
                        tableDataForPropertys.push({
                            name: this.highlightText(docSource.name),
                            documentId: dpItem.documentId,
                            documentCategory: docSource.documentCategory,
                            repositoryId: dpItem.repositoryId,
                            documentProperties: docSource.properties,
                            properties: docSource.properties,
                            itemIds,
                            originText: texts || [],
                            text: _.map(texts, (_txt) => this.highlightText(_txt)).join(' 、 '),
                        });
                        copyDocuments.splice(docIndex, 1);
                    }
                }
            });

            let regDocuments = [];
            copyDocuments.forEach((_doc) => {
                if (_doc.name.indexOf(this.keyWord) !== -1) {
                    _doc.name = this.highlightText(_doc.name);
                    regDocuments.push(_doc);
                }
            });
            let sort = [[], []];
            sort[0] = tableDataForPropertys;
            sort[1] = _.map(regDocuments, (_doc) => {
                _doc.name = this.highlightText(_doc.name);
                return _doc;
            });

            let tableData = _.flattenDeep(sort);
            tableData = this.getPropertiesForPidNo(tableData);
            this.exportPidDocumentsList = tableData.map((item) => {
                return {
                    name: item.name,
                    text: (item.originText && item.originText.join('，')) || '',
                };
            });
            let arr = [...tableData];
            let allComponentsPidData = arr.splice(0, 4);
            console.log('loadPidData--loadPidData', allComponentsPidData)
            bus.$emit('loadPidData', allComponentsPidData);
            this.pidContentList = tableData
            this.publish("searcherVmpidContentList", tableData)
            // bus.$emit("searcherVmpidContentList", tableData);
        } catch (error) {
            console.log('error', error);
            this.pidContentList = []
            this.publish("searcherVmpidContentList", [])
        }
    }

    // ES滚动查询
    async getEsDocByPropertiesMatchphrase(repoIds, query) {
        let destResults = [];
        const {data: esResults} = await this.getFilterResults(repoIds, query);
        destResults = destResults.concat(esResults.hits.hits);
        const scrollId = esResults._scroll_id;
        const count = Math.ceil(esResults.hits.total / this.DEFAULT_SIZE);
        for (let i = 0; i < count; i++) {
            if (destResults.length < esResults.hits.total) {
                const {data: newResults} = await this.getScrollResults(scrollId);
                destResults = destResults.concat(newResults.hits.hits);
            } else {
                break;
            }
        }
        return destResults;
    }

    // ES滚动查询
    async getFilterResults(repoIds, filterObj) {
        let url = '';
        for (let i = 0; i < repoIds.length; i++) {
            if (i === repoIds.length - 1) {
                url = url + repoIds[i] + '_item';
            } else {
                url = url + repoIds[i] + '_item,';
            }
        }
        return Api.getItemsFromES(url, filterObj);
    }

    // ES滚动查询
    async getScrollResults(scrollId) {
        return Api.getCategoryFilterResultsByScroll(scrollId);
    }

    /**
     * pid检索滚动查询
     * @param {*} repositoryId
     * @param {*} targetIds
     * @return {*} itemWithPropertyForEs
     */
    async getAllBatchPropertiesForEs(repositoryId, targetIds) {
        let itemWithPropertyForEs = [];
        let {data: firstResult} = await Api.batchPropertiesForEs({
            targetIds,
            repositoryId,
            size: this.DEFAULT_PROPERTY_SIZE,
        });
        itemWithPropertyForEs = itemWithPropertyForEs.concat(firstResult.results);
        const count = Math.ceil(firstResult.total / this.DEFAULT_PROPERTY_SIZE);
        const scrollId = firstResult.scrollId;

        for (let i = 0; i < count; i++) {
            if (itemWithPropertyForEs.length < firstResult.total) {
                const {data: newResults} = await Api.scrollBatchPropertiesForEs(scrollId);
                itemWithPropertyForEs = itemWithPropertyForEs.concat(newResults.results);
            } else {
                break;
            }
        }
        return itemWithPropertyForEs;
    }

    /**
     * 找出属性信息里与文档对应的位号信息
     * @param {*} documentId
     * @param {*} documentPropertys
     */
    findProperties(documentId, documentPropertys) {
        let itemIds = [],
            texts = [];
        documentPropertys.forEach((item) => {
            if (item.documentId === documentId) {
                itemIds.push(item.itemId);
                texts.push(this.getPropertiesValue(item.properties));
            }
        });
        return {itemIds, texts};
    }
    /**
     * 找出位号信息
     * @param {*} documentId
     * @param {*} documentPropertys
     */
    getPropertiesValue(properties) {
        for (const key in properties) {
            if (Object.hasOwnProperty.call(properties, key)) {
                const element = properties[key];
                if (this.searchConfigs.PIDPropertyNames.includes(key)) {
                    return element.value;
                }
            }
        }
    }

    /**
     * 找出列表中存在图纸编号的数据，找到最新版本，保留最新版本一条数据
     * @param {*} tableData
     */
    getPropertiesForPidNo(tableData) {
        let noNoItems = [];
        let obj = {};
        let sortTableData = [];
        tableData.forEach((item) => {
            let properties = item.properties || null;
            if (properties) {
                if (properties && properties.图纸编号 && properties.图纸编号.value) {
                    if (!obj[properties.图纸编号.value]) {
                        obj[properties.图纸编号.value] = [];
                        obj[properties.图纸编号.value].push(item);
                    } else {
                        obj[properties.图纸编号.value].push(item);
                    }
                } else {
                    noNoItems.push(item);
                }
            } else {
                noNoItems.push(item);
            }
        });
        for (const key in obj) {
            if (Object.hasOwnProperty.call(obj, key)) {
                const element = obj[key];
                element.forEach((item) => {
                    if (item.properties && item.properties.版本) {
                        item.version = item.properties.版本.value || 0;
                    } else {
                        item.version = 0;
                    }
                });
                obj[key] = element.sort(this.sortDrawingFun);
            }
        }

        for (const key in obj) {
            if (Object.hasOwnProperty.call(obj, key)) {
                const element = obj[key];
                const resultMap = new Map();
                for (let i = 0; i < element.length; i++) {
                    const {repositoryId, name, version} = element[i];
                    const key = `${repositoryId}-${name}`;
                    if (!resultMap.has(key) || version > resultMap.get(key).version) {
                        resultMap.set(key, element[i]);
                    }
                }
                resultMap.forEach((value) => {
                    sortTableData.push(value);
                });
            }
        }
        // console.log('sortTableData', sortTableData);
        // console.log('noNoItems', noNoItems);
        return [...sortTableData, ...noNoItems];
    }

    /**
     * 按版本号排序
     */
    sortDrawingFun(a, b) {
        if (a.version && b.version) {
            try {
                const regNumber = /[0-9]+([.]{1}[0-9]+){0,1}/g;
                let aNumber = null,
                    bNumber = null;
                if (a.version.match(regNumber) && a.version.match(regNumber)[0]) {
                    if (a.version.match(regNumber)[0].split('.').length > 1) {
                        aNumber = a.version.match(regNumber)[0].split('.')[0];
                    } else {
                        aNumber = a.version.match(regNumber)[0];
                    }
                }
                if (b.version.match(regNumber) && b.version.match(regNumber)[0]) {
                    if (b.version.match(regNumber)[0].split('.').length > 1) {
                        bNumber = b.version.match(regNumber)[0].split('.')[0];
                    } else {
                        bNumber = b.version.match(regNumber)[0];
                    }
                }

                if (aNumber === bNumber) {
                    if (
                        a.version.match(regNumber)[0].split('.').length > 1 ||
                        b.version.match(regNumber)[0].split('.').length > 1
                    ) {
                        let aSecondNumber = null,
                            bSecondNumber = null;
                        if (a.version.match(regNumber)[0].split('.').length > 1) {
                            aSecondNumber = a.version.match(regNumber)[0].split('.')[1];
                        } else {
                            aSecondNumber = 0;
                        }
                        if (b.version.match(regNumber)[0].split('.').length > 1) {
                            bSecondNumber = b.version.match(regNumber)[0].split('.')[1];
                        } else {
                            bSecondNumber = 0;
                        }
                        return bSecondNumber - aSecondNumber;
                    }
                } else {
                    return bNumber - aNumber;
                }
            }catch (e) {
                console.log(e)
            }
        }
    }

    /**
     * 获取切换厂区后的pid文档数据
     * @param {*} globalModels
     * @return {*}
     */
    getGlobalDataDocuments(globalModels) {
        let pidRepositoryWithDoc = [];
        return Promise.all(
            globalModels.map(async (proId) => {
                return Api.getPidDocuments({repositoryId: proId.id}).then((res) => {
                    pidRepositoryWithDoc.push({
                        repositoryId: proId.id,
                        list: res.data.map((repo) => {
                            return repo.id;
                        }),
                    });
                    res.data.forEach((doc) => {
                        doc.repositoryId = proId.id;
                    });
                    return res.data;
                }).catch(() => {
                    return [];
                });
            }),
        )
            .then((result) => {
                return _.flattenDeep(result);
            })
            .catch((error) => {
                console.log(error);
            });
    }


    serializeItemData(_datas){
        const results = [];
        const reg = new RegExp(this.keyWord, 'gi');
        let cur = this;
        _datas.forEach(function (data) {
            const result = {};
            const d = data._source;
            const repository = data._index.substr(0, data._index.indexOf('_'));
            if (d.categories && d.categories.length > 0) {
                result.category = (d.categories.length > 0 && d.categories.join('，')) || [];
                if (result.category.search(reg) > -1) {
                    result.category = cur.highlight(result.category);
                }
            }
            if (typeof d.properties['名称'] !== 'undefined' && d.properties['名称'].value) {
                result.name = d.properties['名称'].value;
                if (result.name.search(reg) > -1) {
                    result.name = cur.highlight(result.name);
                }
            }
            if (typeof d.properties['装置名称'] !== 'undefined' && d.properties['装置名称'].value) {
                result.device = d.properties['装置名称'].value;
                if (result.device.search(reg) > -1) {
                    result.device = cur.highlight(result.device);
                }
            }
            if (typeof d.properties['位号'] !== 'undefined' && d.properties['位号'].value) {
                result.tagNumber = d.properties['位号'].value;
                if (result.tagNumber.search(reg) > -1) {
                    result.tagNumber = cur.highlight(result.tagNumber);
                }
            }
            result.itemId = data._id;
            result.repositoryId = repository;
            results.push(result);
        });
        return results;
    }

    highlight(oldStr) {
        const reg = new RegExp(this.keyWord, 'gi');
        const newStr = oldStr.replace(reg, function (txt) {
            return '<span style="color:#EA4436">' + txt + '</span>';
        });
        return newStr;
    }


    async searchDocuments(_name, _category, _excludeCategory) {
        if (this.repoUtil.docRepoModelsObj === null) {
            return;
        }
        const result = [];
        this.documents = await this.repoUtil.docRepoModelsObj.getDocuments(_category);

        for (const doc of this.documents) {
            if (
                _.isUndefined(doc.name) ||
                doc.name.toLowerCase().indexOf(_name.toLowerCase()) === -1 ||
                doc.documentCategory === _excludeCategory
            ) {
                continue;
            }
            result.push(doc);
        }
        return result;
    }

    async searchDocumentsByCate(_category, _excludeCategory) {
        if (this.repoUtil.docRepoModelsObj === null) {
            return;
        }
        const result = [];
        this.documents = await this.repoUtil.docRepoModelsObj.getDocuments(_category);

        for (const doc of this.documents) {
            if (doc.documentCategory === _excludeCategory) {
                continue;
            }
            result.push(doc);
        }
        return result;
    }
}