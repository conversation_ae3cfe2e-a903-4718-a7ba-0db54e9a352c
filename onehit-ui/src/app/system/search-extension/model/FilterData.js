/*
 * @Descriptin:
 * @Version: 0.1
 * @Autor: <PERSON>
 * @Date: 2022-04-25 10:13:11
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-28 13:47:01
 */
class FilterData {
	constructor(searchModel) {
		this.searchModel = searchModel;
	}

	/**
	 * @descripton: 通过页码筛选工厂对象数据
	 * @param {*} pageNo
	 * @param {*} pageSize
	 * @return {*}
	 * @author: Gary
	 */
	get_FactoryList({ pageNo = 1, pageSize = null }) {
		let result = [...this.searchModel.factoryContentList];
		if (!pageSize) {
			return result;
		}
		return {
			result: result.splice((pageNo - 1) * pageSize, pageSize),
			total: this.searchModel.factoryContentTotal,
		};
	}

	/**
	 * @descripton: 通过页码筛选pid数据
	 * @param {*} pageNo
	 * @param {*} pageSize
	 * @return {*}
	 * @author: Gary
	 */
	get_PidList({ pageNo = 1, pageSize = null }) {
		let result = [...this.searchModel.pidContentList];
		if (!pageSize) {
			return result;
		}
		return {
			result: result.splice((pageNo - 1) * pageSize, pageSize),
			total: this.searchModel.pidContentList.length,
		};
	}

	/**
	 * @descripton: 通过页码筛选文档数据
	 * @param {*} pageNo
	 * @param {*} pageSize
	 * @return {*}
	 * @author: Gary
	 */
	get_DocList({ pageNo = 1, pageSize = null, filter = null }) {
		let result = [...this.searchModel.docContentList];
		if (!pageSize) {
			return result;
		}
		if (filter && (filter.categories.length > 0 || Object.keys(filter.property).length > 0 || filter.repoModels.length > 0)){
			const documents = [];
			let total = 0;
			for (let i = 0; i < result.length; i++) {
				const document = result[i];
				if (checkRepoModel(document, filter.repoModels) &&
					checkCategory(document, filter.categories) &&
					checkProperty(document, filter.property)){
					documents.push(document);
					total++;
				}
			}
			return {
				result: documents.splice((pageNo - 1) * pageSize, pageSize),
				total: total,
			};
		}

		return {
			result: result.splice((pageNo - 1) * pageSize, pageSize),
			total: this.searchModel.docContentList.length,
		};
	}
}

export default FilterData;

function checkRepoModel(document, repoModels){
	if (repoModels.length === 0){
		return true;
	}
	const repoModel = repoModels.filter(repoModel => repoModel.repositoryId === document.repositoryId)
	if (repoModel.length > 0){
		return true;
	}else {
		return false;
	}
}

function checkCategory(document, categories){
	if (categories.length === 0){
		return true;
	}
	const _categories = document.categories.filter(catgory => categories.includes(catgory));
	if (_categories.length > 0){
		return true;
	}else {
		return false
	}
}

function checkProperty(document, property){
	if (Object.keys(property).length === 0){
		return true;
	}
	for (let propertyName in property){
		if (document.properties && document.properties[propertyName]
			&& property[propertyName].includes(document.properties[propertyName].value)){
			return true
		}
	}
	return false
}
