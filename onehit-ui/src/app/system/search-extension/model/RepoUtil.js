import {modelManager} from "finger";

export class RepoUtil{
    constructor(searchModel) {
        this.searchModel = searchModel;
        // RepoModels 对象
        this.pidRepoModelsObj = null;
        this.docRepoModelsObj = null;
        this.itemRepoModelsObj = null;
        // RepoModel 对象
        this.pidRepoModels = null;
        this.docRepoModels = null;
        this.itemRepoModels = null;
    }

    // 校验数据权限
    async checkPermission(){
        if (!this.pidRepoModels || !this.docRepoModels || !this.itemRepoModels){
            await this.getRepoModelByLabel();
        }
        return {
            factoryRepository: this.itemRepoModels,
            pidRepository: this.pidRepoModels,
            docRepository: this.docRepoModels,
        }
    }

    /**
     * 获取数据模型
     * @param options 筛选条件 例如：[{"type":"LabelsFilter","labels":[{"key":"装置名称","value":"苯乙烯装置"}]}]
     * @return {Promise<*>}
     */
    async requestRepoModels(options = []) {
        const repositories = await modelManager.request('RepoModels', options);
        return repositories[0];
    }

    async getRepoModelByLabel(){
        const filters = { type: 'TasksFilter', tasks: [{ type: 'BUILD_INDEX_TASK', status: 'SUCCESS' }] };
        this.pidRepoModelsObj = await this.requestRepoModels(this.getFilterOption("P&ID"));
        this.docRepoModelsObj = await this.requestRepoModels(this.getFilterOption("文档"));
        this.itemRepoModelsObj = await this.requestRepoModels(this.getFilterOption("模型"));
        this.pidRepoModels = await this.pidRepoModelsObj.getHasSearchIndex(filters);
        this.docRepoModels = await this.docRepoModelsObj.getHasSearchIndex(filters);
        this.itemRepoModels = await this.itemRepoModelsObj.getHasSearchIndex(filters);
    }

    getFilterOption(type){
        const lable = this.searchModel.searchConfigs.repositoriesConfig.find(item => item.type === type);
        return {
            filters: JSON.stringify([{ "type": "LabelsFilter", "labels": [{ "key": lable.key, "value": lable.value }] }]),
        };
    }
}