/*
 * @Descriptin:
 * @Version: 0.1
 * @Autor: <PERSON>
 * @Date: 2022-04-12 15:28:09
 * @LastEditors: --
 * @LastEditTime: 2023-06-02 15:27:35
 */
import SearchVue from 'vue';
import App from './app.vue';
// import {Vuex,store} from '@/store/index';
import store from '@/store';
import _ from 'lodash-oneHit';
import VueI18n from 'vue-i18n';
import Bus from '@/app/system/search-extension/utils/Bus';
import '@/assets/search/reset.css';
import enLocale from 'element-ui/lib/locale/lang/en';
import zhLocale from 'element-ui/lib/locale/lang/zh-CN';
import ruLocale from 'element-ui/lib/locale/lang/ru-RU';
import en_US from '@/languages/en_US.json';
import zh_CN from '@/languages/zh_CN.json';
import ru_RU from '@/languages/ru_RU.json';
import ElementUI from 'element-ui';


// SearchVue.use(Vuex);
SearchVue.use(_);
SearchVue.use(VueI18n);
SearchVue.prototype.$bus = new Bus();
SearchVue.config.productionTip = false;

const locale = localStorage.getItem('lang');

const i18n = new VueI18n({
	locale: locale ? locale : 'zh_CN', // 语言标识 //this.$i18n.locale // 通过切换locale的值来实现语言切换
	messages: {
		zh_CN: Object.assign(zh_CN, zhLocale),
		en_US: Object.assign(en_US, enLocale),
		ru_RU: Object.assign(ru_RU, ruLocale),
	},
	silentTranslationWarn: true,
});

SearchVue.use(ElementUI, { size: 'small', i18n: (key, value) => i18n.t(key, value) });

let SearchDom = document.createElement('div');
SearchDom.id = 'SearchWrap';
SearchDom.style.cssText = 'position: absolute;top:0,left:0;z-index: -100;';
document.querySelector('body').appendChild(SearchDom);

const searcherVm = new SearchVue({
	i18n,
	store,
	render: (h) => h(App),
}).$mount('#SearchWrap');
export default searcherVm;
