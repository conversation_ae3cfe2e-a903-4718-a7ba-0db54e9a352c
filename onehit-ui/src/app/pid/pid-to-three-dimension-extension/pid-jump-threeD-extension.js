import {PidToRender3DView} from './PidToRender3DView';
import {sceneManager} from 'finger';
import $ from 'jquery';
import Vue from 'vue';
import store from '@/store';
import PidToRender3D from './PidToRender3D.vue';
import ElementUI from "element-ui";
import DocPreviewRightPickExt from "@/app/extension/pid/DocPreViewRightPickExt";
import {globalState} from "finger";

Vue.use(ElementUI)

$('#Pid3DView_containerId').find('.container-header').css({ display: 'none' });


let Dom = document.createElement('div');
Dom.id = 'TwoThreeJumpWrap';
Dom.style.cssText = 'position: absolute;top:0,left:0;z-index: -100;';
document.querySelector('body').appendChild(Dom);

const vm = new Vue({
	store,
	render: (h) => h(PidToRender3D),
}).$mount('#TwoThreeJumpWrap');
const pidToRender3DView = new PidToRender3DView({vm: vm});
/**
 * 三维跳二维扩展实例
 *
 */
class PidToRender3DExtension extends DocPreviewRightPickExt{
	checkHandleCondition() {
		const execPlatforms = ["OneHit-BusinessWorkbench"];
		if (globalState && !execPlatforms.includes(globalState.get("platform"))) {
			return false;
		}
		const { common } = store.state.config;
		const PIDName = common.pidSceneName;
		const sceneName = sceneManager.getCurrentScene().name;
		return PIDName === sceneName;
	}

	async handleMenu(itemModels, options) {
		if (itemModels && itemModels.length > 1) {
			const disabledMenu = {
				text: "查看三维模型",
				order: 3,
				disabled: true,
				handler: () => {
				}
			}
			return [disabledMenu];
		}
		const menu = {
			text: "查看三维模型",
			order: 3,
			disabled: false,
			handler: () => {
				if (!itemModels || itemModels.length === 0) {
					pidToRender3DView.showErrorMessage("未匹配到模型仓库信息")
					return [];
				} else {
					pidToRender3DView.toJump(itemModels[0]);
				}
			}
		}
		return [menu];
	}

	handleEvent(itemModels, options) {
		return this.handleMenu(itemModels, options);
	}
}

export const pidToRender3DExtension = new PidToRender3DExtension();
