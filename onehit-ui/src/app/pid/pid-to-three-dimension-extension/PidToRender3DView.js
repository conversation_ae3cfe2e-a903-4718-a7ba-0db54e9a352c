import _ from 'lodash-oneHit';
import $ from 'jquery';
import { Api } from '@/api';
import { sceneManager, modelManager } from 'finger';
import { store } from '@/store';
import './pid.css';

const CHECK_MIXED = '请检查数据仓库是否混合';
const MIXED_FILED = '数据仓库混合失败';
const NO_TAG = '当前选中对象无位号信息';
const NO_R3DSCENE = '三维场景名称不正确或场景不存在';
const NO_R3DITEM = '模型数据中无该对象位号信息';
const NO_R3DREPOSITORYID = "未匹配到模型仓库信息"
/**
 * 二维跳转三维View实例
 *
 */
export class PidToRender3DView {
	constructor(options) {
		this.vm = options.vm;
		this.searchConfigs = null;
		this.currentModel = null;
		this.searchViewModel = null;
		this.dataModels = null;
		this.queryString = null;
		this.commonConfig = null;
		this.pipeLineConfig = null;
		this.pipe = null;
		this.isSpecial = false;
	}

	/**
	 * 二维跳转三维View类型
	 *
	 */

	getType () {
		return 'PidToRender3DView';
	}

	showErrorMessage(message) {
		this.vm.$message({
			dangerouslyUseHTMLString: true,
			duration: 3000,
			customClass: 'my_briefMsg scenatorStyle scenator_briefMsg warn',
			message: message,
		});
	}

	// main
	async toJump (itemModel) {
		let _this = this;
		// 初始化置空弹窗列表数据
		_this.vm.$store.state.pidDom = [];
		_this.vm.$store.state.pid_show = false;
		_this.vm.$store.state.r3d_item_list = [];
		try {
			const { twoThreeDimensionJump, common } = store.state.config
			_this.commonConfig = twoThreeDimensionJump;
			_this.commonConfig.render3D.sceneName = common.render3dSceneName
			_this.commonConfig.PID.sceneName = common.pidSceneName
			_this.currentModel = itemModel;
			_this.vm.$store.state.commonConfig = _this.commonConfig;
			// 1.判断仓库是否混合
			const { isMix, msg } = await _this.isMixed(_this.currentModel);
			if (!isMix) {
				_this.vm.$message({
					dangerouslyUseHTMLString: true,
					duration: 3000,
					customClass: 'my_briefMsg scenatorStyle scenator_briefMsg warn',
					message: msg,
				});
				return;
			}
			// 2.判断位号是否存在
			if (!_this.currentModel.tag) {
				_this.vm.$message({
					dangerouslyUseHTMLString: true,
					duration: 3000,
					customClass: 'my_briefMsg scenatorStyle scenator_briefMsg warn',
					message: NO_TAG,
				});
				return;
			}
			_this.vm.$store.state.tag = _this.currentModel.tag;
			// 3.获取点击的item的所有祖先
			const ancestors = await _this.currentModel.getAncestorsInfo();
			// 获取render3D管线定义的配置
			const { data: pipeLineConfig } = await Api.getPipelineConfig();
			_this.pipeLineConfig = pipeLineConfig;
			if (_this.pipeLineConfig != null) {
				let configs = _this.pipeLineConfig.PipeLineConfig.categoryName;
				// 判断是否为管线
				const pipe = _.find(ancestors, (item) => {
					if (JSON.stringify(configs).includes(item.categoryName)) {
						return item;
					}
				});
				if (pipe) {
					let special = _this.currentModel.categories.filter((spe) => {
						if (_this.commonConfig.SpecialCategory.category.includes(spe)) {
							return spe;
						}
					});
					if (special.length > 0) {
						_this.pipe = pipe;
						_this.isSpecial = true;
					}
				}
			}
			_this.jumpTo3D();
		} catch (error) {
			console.log("error", error);
		}

	}

	/**
	 * @descripton: 如果是特殊分类
	 * @param {*}
	 * @return {*}
	 * @author: Gary
	 */
	async isSpecialControl () {
		// 获取管段的repoModel
		const repoModel = this.currentModel.repoModel;
		// 获取管段的itemModel
		const pipeItemModel = await repoModel.requestItemModel(this.pipe.id);
		this.currentModel = pipeItemModel;
		// 3.判断位号是否存在
		if (!this.currentModel.tag) {
			$.messager.show({
				type: 'info',
				msg: '3D中无该对象位号信息!',
				timeout: 1000,
			});
			return;
		}
		this.jumpTo3D();
	}

	/**
	 * @descripton: 跳转PID
	 * @param {*}
	 * @return {*}
	 * @author: Gary
	 */
	async jumpTo3D () {
		let _this = this;
		// 获取三维场景
		const render3DScene = await sceneManager.request({
			name: _this.commonConfig.render3D.sceneName,
		});
		// 判断跳转三维时是否有对应仓库
		const repoModel = render3DScene.data.models.find((modelItem) => modelItem.type === "RepoModel")
		const PidRepository = await modelManager.request("RepoModel", repoModel.options);
		var findPidRepositoryInfo = PidRepository.find(repositoryItem => repositoryItem.repositoryId === _this.currentModel.repositoryId)
		if (!findPidRepositoryInfo) {
			_this.vm.$message({
				dangerouslyUseHTMLString: true,
				duration: 3000,
				customClass: 'my_briefMsg scenatorStyle scenator_briefMsg warn',
				message: NO_R3DREPOSITORYID,
			});
			return;
		}
		// 获取左侧导航树菜单名称为三维的dom
		// 5.跳转至三维
		const repId = _this.currentModel.repositoryId;
		const tag = _this.currentModel.tag;
		const params = {
			"query": {
				"term": {
					"tag.keyword": tag
				}
			},
			"size": 100
		}
		try {
			const { data: result } = await Api.getItemByTag(repId, params);
			const lists = result.hits.hits;
			const Ids = [];
			lists.forEach((item) => { Ids.push(item['_id']); });
			// 获取reference
			const { data: reference } = await Api.getBulkReferences({
				repositoryId: _this.currentModel.repositoryId,
				targetItemIds: Ids
			});
			// 获取psdata行位数据，此数据唯一
			const { data: psdata } = await Api.getPsdata(_this.currentModel.repositoryId);
			// 筛选出sourceDocumentId与行位数据id一致的数据
			const sourceDocuments = reference.filter((ref) => ref.sourceDocumentId === psdata.id);
			// 提取item的id
			const targetItems = sourceDocuments.map((doc) => {
				return doc.targetItemId;
			});
			// 根据筛选_doc
			const itemsList = lists.filter((item) => {
				if (targetItems.indexOf(item._id) > -1) {
					return item;
				}
			});
			if (itemsList.length > 0) {
				// 获取左侧导航树菜单名称为三维的dom
				const doms = $(`.nav-menu-text:contains("${_this.commonConfig.render3D.sceneName}")`);
				const pidDom = [];
				for (let i = 0; i < doms.length; i++) {
					if (doms[i].innerText === _this.commonConfig.render3D.sceneName) {
						pidDom.push(doms[i]);
					}
				}
				if (pidDom && pidDom.length > 0) {
					pidDom[0].click();
				}
				// 获取三维对象的itemModel并展示跳转列表
				itemsList.forEach(async function (ele) {
					// 获取对象的itemModel
					const repoModel = _this.currentModel.repoModel;
					const res = await repoModel.requestItemModels([ele._id]);
					// 如果对象的数量为1，则直接跳转
					if (itemsList.length === 1) {
						// 跳转三维场景
						if (pidDom.length > 0) {
							pidDom[0].click();
						} else {
							_this.vm.$message({
								dangerouslyUseHTMLString: true,
								duration: 3000,
								customClass: 'my_briefMsg scenatorStyle scenator_briefMsg warn',
								message: NO_R3DSCENE,
							});
							return;
						}
						render3DScene.state.set('currentModel', res);
					} else if (itemsList.length > 1) {
						// 跳转三维场景
						if (pidDom.length > 0) {
							_this.vm.$store.state.pidDom = pidDom;
						} else {
							_this.vm.$message({
								dangerouslyUseHTMLString: true,
								duration: 3000,
								customClass: 'my_briefMsg scenatorStyle scenator_briefMsg warn',
								message: NO_R3DSCENE,
							});
							return;
						}
						_this.vm.$set(
							_this.vm.$store.state.r3d_item_list,
							_this.vm.$store.state.r3d_item_list.length,
							res[0],
						);
						_this.vm.$store.state.pid_show = true;
					}
				});
			} else {
				_this.vm.$message({
					dangerouslyUseHTMLString: true,
					duration: 3000,
					customClass: 'my_briefMsg scenatorStyle scenator_briefMsg warn',
					message: NO_R3DITEM,
				});
			}
		} catch (error) {
			console.error(error);
		}
	}

	/**
	 * @descripton: 查看当前仓库是否已经做混合
	 * @param {*} itemModel 当前选中的对象
	 * @return {boolean} 返回truefalse异常信息
	 * @author: Gary
	 */
	async isMixed (itemModel) {
		const mixed = itemModel.repoModel.tasks.filter((task) => {
			if (task.type === this.commonConfig.PID.repositoriesMixed.type) {
				return task;
			}
		});
		// 如果仓库不存在混合对象标识
		if (mixed.length === 0) {
			return {
				isMix: false,
				msg: CHECK_MIXED,
			};
		}
		// 如果混合仓库标识对象状态为失败
		if (mixed[0].status === this.commonConfig.PID.repositoriesMixed.status) {
			return {
				isMix: true,
				msg: '',
			};
		} else {
			return {
				isMix: false,
				msg: MIXED_FILED,
			};
		}
	}
}
