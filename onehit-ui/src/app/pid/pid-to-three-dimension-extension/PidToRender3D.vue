<!--
 * @Descriptin:
 * @Version: 0.1
 * @Autor: <PERSON>
 * @Date: 2022-02-28 15:12:09
 * @LastEditors: wangzelin
 * @LastEditTime: 2023-01-13 10:03:58
-->
<template>
	<div>
		<el-dialog
			class="scenatorStyle"
			title="跳转三维"
			:visible.sync="$store.state.pid_show"
			width="320px"
			top="0"
			:append-to-body="true"
		>
			<ul class="device-list">
				<li class="jumpToRender3D" v-for="(item, idx) in r3d_list" :key="idx">
					<div class="item" @click="jumpToRender3D">
						<span class="pidTag">{{ $store.state.tag }}</span>
						<span class="deviceName">{{ getDeviceName(item) }}</span>
					</div>
				</li>
			</ul>
		</el-dialog>
	</div>
</template>

<script>
import { sceneManager } from 'finger';
export default {
	name: 'PidToRender3D',
	data() {
		return {
			r3d_list: [],
		};
	},
	computed: {
		list: function () {
			return this.$store.state.r3d_item_list;
		},
	},
	watch: {
		list(newList) {
			this.r3d_list = newList;
		},
	},
	methods: {
		/**
		 * @descripton: 获取装置名称
		 * @param {*} item
		 * @return {*}
		 * @author: Gary
		 */
		getDeviceName(item) {
			if (item.properties['name']) {
				return item.properties['name'].value;
			}
			if (item.properties['名称']) {
				return item.properties['名称'].value;
			}
			return '';
		},
		/**
		 * @descripton: 跳转到三维并定位
		 * @param {*} item 对象的itemModel
		 * @return {*}
		 * @author: Gary
		 */
		async jumpToRender3D() {
			if (this.$store.state.pidDom.length > 0) {
				this.$store.state.pid_show = false;
				this.$store.state.pidDom[0].click();
				try {
					const render3DScene = await sceneManager.request({
						name: this.$store.state.commonConfig.render3D.sceneName,
					});
					if(render3DScene) {
						render3DScene.state.set('currentModel', this.r3d_list);
					}

				} catch (error) {
					console.log("error", error)
				}

			}
		},
	},
};
</script>

<style lang="less" scoped>
.device-list {
	height: 150px;
	max-height: 400px;
	overflow-x: auto;
}
.jumpToRender3D {
	padding: 10px;
	border-bottom: 1px solid rgba(0, 0, 0, 0);
	&:hover {
		border-bottom: 1px solid #0854a0;
	}
}
.item {
	width: 100%;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	align-items: center;
	cursor: pointer;
}
.allow {
	width: 12px;
	height: 12px;
	display: inline-block;
	border-radius: 50%;
	margin-right: 8px;
	background-color: green;
	box-shadow: -12px 3px 3px yellowgreen inset, 0 0 4px green, 2px -1px 4px yellowgreen;
}
.refuse {
	width: 12px;
	height: 12px;
	display: inline-block;
	border-radius: 50%;
	margin-right: 8px;
	background-color: red;
	box-shadow: -12px 6px 6px red inset, 0 0 4px red, 2px -1px 4px red;
}
.deviceName,
.pidTag {
	margin-left: 10px;
}
.my_briefMsg {
  min-width: 192px !important;
  max-width: 536px !important;
  background: white !important;
  padding: 14px 16px !important;
  box-shadow: 0px 10px 30px 0px rgb(0 0 0 / 10%), 0px 0px 0px 1px rgb(0 0 0 / 10%) !important;
  border-radius: 4px !important;
  border: 0px !important;
  .el-message__content {
    color: #182a4e !important;
  }
}
</style>
