<template>
    <div id="draw_parent_table" v-show="isPreviewDrawingsVersion">
        <div class="go_back" :class="{'go-back-fullscreen': isFullScreen}" :style="{left: left}">
            <div class="go_back_btn" @click="goBack">
                <i class="edc-icon-left" style="margin-right: 8px;vertical-align: bottom;color: #0854A1"></i>返回
            </div>
        </div>
        <div style="margin-top: 30px; padding: 20px">
            <table-toolbar :filterLength="selectFilterData.length">
                <template v-slot:search>
                    <el-input
                        ref="inputOutSearch"
                        placeholder="搜索"
                        prefix-icon="el-icon-search"
                        size="small"
                        @input="search"
                        v-model.trim="searchText"
                        clearable>
                    </el-input>
                </template>
                <template v-slot:filter>
                    <select-filter-group
                        ref="selectFilterGroup"
                        :select-filter-data="selectFilterData"
                        @changeChecked="handleFilterChange">
                    </select-filter-group>
                </template>
            </table-toolbar>
            <div
                :style="{height: tableIsLoading ? parseInt(tableMaxHeight) + 'px' : 'fit-content'}"
                style="position: relative">
                <table-loading :table-height="tableMaxHeight" v-show="tableIsLoading"/>
                <el-table
                    stripe
                    border
                    auto-fit-column
                    ref="table"
                    highlight-current-row
                    :data="drawingsData"
                    :max-height="tableMaxHeight"
                    :header-cell-style="{ background: '#EFEFEF' }"
                    class="drawingsTable"
                    :row-class-name="rowClassName"
                    :default-sort="{prop: 'updateTime', order: 'descending'}"
                    @sort-change="sortChange">
                    <el-table-column type="expand" v-slot="{ row }">
                        <historical-version-table
                            :rowTable="row"
                            :searchText="searchText"
                            :showColumns="showColumns"
                            @viewDigitalDrawing="viewDigitalDrawing"
                            @handleClickFile="handleClickFile">
                        </historical-version-table>
                    </el-table-column>
                    <el-table-column
                        show-overflow-tooltip
                        width="260"
                        sortable="custom"
                        prop="name"
                        class-name="drawing-name"
                        label="图纸文件">
                        <template v-slot="{ row }">
                            <el-link
                                type="primary"
                                :underline="false"
                                :disabled="row.parseStatus!==2"
                                v-html="filterHighLight(row.name + '.' + row.suffix)"
                                style="overflow:hidden;display:block;text-overflow:ellipsis"
                                @click="viewDigitalDrawing(row)">
                            </el-link>
                        </template>
                    </el-table-column>
                    <el-table-column
                        show-overflow-tooltip
                        v-for="item in showColumns"
                        :width="item.type === 'file' ? 260 : 200"
                        :key="item.id"
                        :prop="item.name"
                        :label="item.name">
                        <template v-slot="{ row }">
                            <div v-if="item.type !== 'file'">
                                <span v-if="row[item.name] && item.type !== 'select'"
                                      v-html="filterHighLight(row[item.name])"></span>
                                <span v-else-if="row[item.name] && item.type === 'select'">{{ row[item.name] }}</span>
                                <span v-else>-</span>
                            </div>
                            <div v-else>
                                <div v-if="row[item.name] && row[item.name].length" class="attachments">
                                    <el-link
                                        v-for="attachment in row[item.name]"
                                        :key="attachment.id"
                                        type="primary"
                                        :underline="false"
                                        v-html="filterHighLight(attachment.fileName + '.' + attachment.fileSuffix)"
                                        @click="handleClickFile(attachment, row)">
                                    </el-link>
                                </div>
                                <span v-else>-</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        show-overflow-tooltip
                        min-width="80"
                        prop="updateUserName"
                        label="更新人">
                        <template v-slot="{ row }">
                            <span v-if="row.updateUserName" v-html="filterHighLight(row.updateUserName)"></span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        show-overflow-tooltip
                        prop="updateTime"
                        min-width="160"
                        label="更新时间"
                        sortable="custom">
                        <template v-slot="{ row }">
                            <span v-if="row.updateTime">{{ formatterTime(row.updateTime) }}</span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        show-overflow-tooltip
                        :max-width="250"
                        prop="projectSpaceName"
                        label="所属装置">
                        <template slot-scope="scope">
                            <div v-if="scope.row.projectSpaceName"
                                 style="width: 100%;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">
                                <div
                                    v-if="measureText(scope.row.projectSpaceName.substr(0, scope.row.projectSpaceName.indexOf(searchText) > -1 ? scope.row.projectSpaceName.indexOf(searchText): 0 ), 14, '', scope.column) + 16 > scope.column.realWidth - 26">
                                    <span
                                        :style="{ width: scope.column.realWidth - measureText(searchText, 14) - 70  + 'px'}"
                                        style="display:inline-block;font-size:14px;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">{{ scope.row.projectSpaceName.substr(0, scope.row.projectSpaceName.indexOf(searchText) - 3) }}</span>
                                    <span :style="{width: measureText(searchText, 14) + 50  + 'px'}"
                                          style="display: inline-block;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;"
                                          v-html="filterHighLight(scope.row.projectSpaceName.substr(scope.row.projectSpaceName.indexOf(searchText)-3, scope.row.projectSpaceName.length - 1))"></span>
                                </div>
                                <span v-else v-html="filterHighLight(scope.row.projectSpaceName)"></span>
                            </div>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>

                    <template slot="empty">
                        <table-empty v-show="!tableIsLoading" :table-height="tableMaxHeight"/>
                    </template>
                </el-table>
                <el-pagination
                    v-if="drawingsData && drawingsData.length"
                    small
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 30, 50, 100]"
                    :page-size="pageSize"
                    layout="sizes, prev, pager, next, jumper, ->, total"
                    :total="total">
                </el-pagination>
            </div>
        </div>
        <file-preview v-show="filePreviewFlag" ref="filePreview" @closeAllFile="filePreviewFlag=false"></file-preview>
        <el-dialog class="scenatorStyle" title="历史版本" @close="closeDrawingHistoryDialog" :append-to-body="true"
                   :visible.sync="drawingHistoryVisible" width='480px' top=''>
            <div v-if="currentDrawingHistoryInfo === null || currentDrawingHistoryInfo.historyVersions.length === 0"
                 style="position: relative;width: 100%;height: 218px;">
                <div class="scenatorStyle_empty_container small_container">
                    <div></div>
                    <p class="empty_text">暂无数据</p>
                </div>
            </div>
            <historical-version-table
                v-else
                :rowTable="currentDrawingHistoryInfo"
                :searchText="searchText"
                :showColumns=[]
                :isSingleDrawing="true"
                @viewDigitalDrawing="viewDigitalDrawing"
                @handleClickFile="handleClickFile">
            </historical-version-table>
        </el-dialog>
    </div>
</template>

<script>
import $ from 'jquery'
import ErrorHandler from '@/common/ErrorHandler'
import TableEmpty from '@/components/base/TableEmpty'
import TableLoading from '@/components/base/TableLoading'
import SelectFilterGroup from '@/components/base/SelectFilterGroup'
import TableToolbar from '@/components/base/TableToolbar'
import HistoricalVersionTable from './HistoricalVersionTable.vue'
import TextTool from '@/utils/TextTool'
import { debounce } from 'lodash'
import { oneDigitalDrawingsApi } from '@/api/oneDigitalDrawingsApi'
import FormatUtil from '@/utils/FormatUtil'
import { globalState, extensionManager } from 'finger'
import FingerUtil from '@/utils/FingerUtil'
import { sceneOperation } from '@/utils/sceneOperation'
import store from '@/store/index'

export default {
  name: 'index',
  components: { HistoricalVersionTable, TableEmpty, TableLoading, SelectFilterGroup, TableToolbar },
  data () {
    return {
      left: '217px',
      showColumns: [],
      filePreviewFlag: false,
      isPreView: false,
      tableIsLoading: false,
      searchText: '',
      filterAndValue: {
        updateTimeOrder: 'DESC',
        propNameToValues: {}
      },
      drawingsData: [],
      currentPage: 1,
      pageSize: 30,
      total: 0,
      selectFilterData: [
        {
          title: '装置',
          checkboxData: [],
          paramKey: 'repoIds',
          customPopperClass: 'drawing_filter_group_itemClass'
        }
      ],
      point: null,
      isFullScreen: false,
      repoModels: [],
      drawingList: [
        ['目录树', '目录树'],
        ['设备目录', '目录树'],
        ['标注列表', '目录树'],
        ['属性', '属性'],
        ['相关文档', '属性'],
        ['标注信息', '属性'],
        ['标注附件', '属性']
      ],
      drawingHistory: [['图纸目录', '目录树'], ['图纸属性', '属性']],
      drawingHistoryVisible: false,
      currentDrawingHistoryInfo: null
    }
  },
  computed: {
    titleToParamKey () {
      const map = new Map()
      this.selectFilterData.forEach(({ title, paramKey }) => {
        map.set(title, paramKey)
      })
      return map
    },
    filterAndSearchObserver () {
      return { ...this.filterAndValue }
    },
    tableMaxHeight () {
      return window.innerHeight - 50 - 32 - store.state.filterHeight - 100 -
      (this.drawingsData && this.drawingsData.length ? 48 : 0)
    },
    isPause () {
      return this.$root.isPause
    },
    isPreviewDrawingsVersion () {
      return store.state.isPreviewDrawingsVersion
    },
    isShowDrawingHistory () {
      return store.state.isShowDrawingHistory
    },
    currentDrawingInfo () {
      return store.state.currentDrawingInfo
    },
    currentLoadDocumentInfo () {
      return store.state.currentLoadDocumentInfo
    }
  },
  watch: {
    filterAndSearchObserver () {
      this.debounce()
    },
    async isPause (val) {
      if (!val) {
        this.initDefData()
        this.debounce()
        this.isPreView = false
        store.state.isPreView = this.isPreView
        store.state.isPreviewDrawingsVersion = false
        this.hideOrShowCurrentSceneDom('#digital_drawings_view', 'show')
      }
    },
    currentLoadDocumentInfo (val) {
      const currentScene = FingerUtil.getCurrentScene()
      const documentPreviewView = FingerUtil.getViewInScene(currentScene, 'DocumentPreviewView')
      if (currentScene.name === '数字图纸') {
        if (this.isPreviewDrawingsVersion) {
          this.hideOrShowPidToolBar('hide')
          documentPreviewView.setViewSetting(true, false, true, [], {
            type: 'showPickItem'
          })
        } else {
          documentPreviewView.setViewSetting(true, true, true, [], {
            type: 'showPickItem'
          })
          this.hideOrShowPidToolBar('show')
        }
      }
    },
    isPreviewDrawingsVersion (val) {
        console.log("==================")
      const scene = FingerUtil.getCurrentScene()
      FingerUtil.setCurrentModels(scene, [])
      const documentPreviewView = FingerUtil.getViewInScene(scene, 'DocumentPreviewView')
      if (documentPreviewView.isNeedResume) { documentPreviewView.setNeedResumeState(false) }
      // 当退出历史版本查看页面时
      if (!val) {
        // 显示工具栏按钮
        this.hideOrShowPidToolBar('show')
        // 显示最新图纸浏览页面
        this.hideOrShowCurrentSceneDom('#digital_drawings_view', 'show')
        // 隐藏查看历史版本时的导航树、属性面板。展示盲板，图纸导航树这些Tab
        sceneOperation.showOrHideTab(scene, this.drawingHistory, 'hide')
        sceneOperation.showOrHideTab(scene, this.drawingList, 'show')
        // 调整左右两侧面板高度
        $('.digital_drawings .edc_left_tab_element').removeClass('digitalDrawingsPanelHistory')
        $('.digital_drawings .edc_right_tab_element').removeClass('digitalDrawingsPanelHistory')
        $('.digital_drawings .digital_drawings_view').removeClass('digital_drawings_view_show_history')
        // 如果之前图纸浏览场景打开的图纸
        if (store.state.currentDrawingInfo) {
          store.state.currentDrawingInfo.itemModels = []
          FingerUtil.setCurrentModels(scene, [store.state.currentDrawingInfo])
          documentPreviewView.addModel(store.state.currentDrawingInfo, {
            scale: 2
          })
          // 在这里模拟导航树的点击事件,让图纸上的标签显示
          if ('DocNavigator.LeftClickExtensionPoint' in extensionManager.extensionPoints) {
            const extensionPoint = extensionManager.extensionPoints['DocNavigator.LeftClickExtensionPoint']
            extensionPoint.sendClickEventInfo(store.state.currentDrawingInfo,
              store.state.currentDrawingInfoOptions)
          }
        } else {
          FingerUtil.setCurrentModels(scene, [])
          if (documentPreviewView) {
            documentPreviewView.blankPromptVue.update({ visible: true })
            const $pidToolbarContainer = scene.$content.find('.pid-plugin-toolbar')
            $pidToolbarContainer.hide()
          }
        }
      } else {
        this.isFullScreen && document.exitFullscreen()
        this.hideOrShowPidToolBar('hide')
        this.hideOrShowCurrentSceneDom('#digital_drawings_view', 'hide')
        sceneOperation.showOrHideTab(scene, this.drawingHistory, 'show')
        sceneOperation.showOrHideTab(scene, this.drawingList, 'hide')
        $('.digital_drawings .edc_left_tab_element').addClass('digitalDrawingsPanelHistory')
        $('.digital_drawings .edc_right_tab_element').addClass('digitalDrawingsPanelHistory')
        $('.digital_drawings .digital_drawings_view').addClass('digital_drawings_view_show_history')
        sceneOperation.clickTab(scene, '图纸目录')
        sceneOperation.clickTab(scene, '图纸属性')
        const $pidToolbarContainer = scene.$content.find('.pid-plugin-toolbar')
        $pidToolbarContainer.show()
      }
    },
    workProjectSpaceIds () {
      this.initDefData()
      this.debounce()
      this.isPreView = false
      store.state.isPreView = this.isPreView
      this.hideOrShowCurrentSceneDom('#digital_drawings_view', 'hide')
    },
    filterAndValue: {
      handler () {
        this.currentPage = 1
      },
      deep: true
    },
    isShowDrawingHistory (val) {
      // 获取当前图纸的历史版本
      const scene = FingerUtil.getCurrentScene()
      const $item = scene.$content.find('.pid-toolbar-item').find('.drawing-history')
      if (val) {
        console.log('获取' + store.state.currentDrawingInfo.name + '的历史版本    repositoryId：' + store.state.currentDrawingInfo.repositoryId + '   documentId：' + store.state.currentDrawingInfo.documentId)
        // 给图标添加选中样式
        $item.addClass('drawing-history-active')
        const name = store.state.currentDrawingInfo.name.replace(/\.[^/.]+$/, '')
        oneDigitalDrawingsApi.getPidsHistoryByName(store.state.currentDrawingInfo.repositoryId, name).then((data) => {
          this.drawingHistoryVisible = val
          if (data && data.data && data.data.length > 0) {
            this.currentDrawingHistoryInfo = data.data[0]
            this.drawingHistoryVisible = val
          } else {
            // TODO 这里应该给一个合适的提示
            this.currentDrawingHistoryInfo = null
          }
        }).catch(e => {
          ErrorHandler.formatError(e)
          this.drawingHistoryVisible = !val
          $item.removeClass('drawing-history-active')
        })
      } else {
        this.drawingHistoryVisible = val
        $item.removeClass('drawing-history-active')
      }
    }
  },

  created () {
    const scene = FingerUtil.getCurrentScene()
    sceneOperation.showOrHideTab(scene, this.drawingHistory, 'hide', true)
  },
  async mounted () {
    const scene = FingerUtil.getCurrentScene()
    this.repoModels = scene.modelMap.RepoModel
    this.repoModels.forEach(repoModel => {
      this.selectFilterData[0].checkboxData.push({
        labelText: repoModel.name,
        value: repoModel.repositoryId,
        text: repoModel.name
      })
    })
    this.observe()
    this.initDefData()
    this.debounce = debounce(this.initTableFilter, 500)
    const noExecPlatforms = ['OneHit-BusinessWorkbench']
    if (globalState && noExecPlatforms.includes(globalState.get('platform'))) {
      store.state.isPreView = false
      store.state.isPreviewDrawingsVersion = false
    }
    // 监听全屏事件，用以处理pid图纸全屏时，返回按钮位置问题
    this.fullscreenchangeListener = () => {
      this.isFullScreen = document.fullscreenElement
    }
    document.addEventListener('fullscreenchange', this.fullscreenchangeListener)
    globalState && globalState.watch('repoModelAmount', () => {
      store.state.isPreviewDrawingsVersion = false
    })
  },
  beforeDestroy () {
    document.removeEventListener('fullscreenchange', this.fullscreenchangeListener)
    store.state.isPreviewDrawingsVersion = false
    store.state.currentDrawingInfo = null
    store.state.currentDrawingInfoOptions = null
  },
  methods: {
    observe () {
      const dom = document.querySelector('#nav_menu_container')
      if (dom) {
        const observer = new ResizeObserver(entries => {
          this.left = entries[0].target.offsetWidth + 1 + 'px'
        })
        observer.observe(dom)
      }
    },
    measureText (pText, pFontSize, pStyle, col) {
      return TextTool.measureText(pText, pFontSize, pStyle)
    },
    initDefData () {
      this.filePreviewFlag = false
      this.total = 0
      this.currentPage = 1
      this.pageSize = 30
      this.drawingsData = []
      this.searchText = ''
      this.filterAndValue = {
        updateTimeOrder: 'DESC',
        propNameToValues: {}
      }
      this.tableIsLoading = true
      if (this.$refs.selectFilterGroup) {
        this.$refs.selectFilterGroup.cleanInput()
        this.$refs.selectFilterGroup.cleanChecked()
      }
    },
    getPropResource () {
      oneDigitalDrawingsApi.getPropResource().then(({ data }) => {
        this.showColumns = data
      })
    },
    initTableFilter () {
      this.getPropResource()
      this.tableIsLoading = true
      const body = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        keyWord: this.searchText,
        ...this.filterAndValue
      }

      if (this.filterAndValue.repoIds.length === 0) {
        body.repoIds = this.repoModels.map(repoModel => repoModel.repositoryId)
      }
      oneDigitalDrawingsApi.getPidsModels(body).then(({ data }) => {
        this.tableIsLoading = false
        const handel = (baseData) => {
          if (baseData) {
            return baseData.map(item => {
              return {
                value: item,
                text: item,
                labelText: item
              }
            })
          }
          return []
        }
        Object.keys(data).forEach(key => {
          if (key !== 'page') {
            const index = this.selectFilterData.findIndex(select => select.title === key)
            if (index > -1) {
              this.$set(this.selectFilterData[index], 'checkboxData', handel(data[key]))
            } else {
              this.selectFilterData.push({
                title: key,
                checkboxData: handel(data[key]),
                paramKey: key
              })
            }
          }
        })
        this.selectFilterData = this.selectFilterData.filter(filter => Object.keys((data)).includes(filter.title) || filter.title === '装置')
        data.page.results.forEach(item => {
          item.isDeleting = false
          item.properties.forEach(prop => {
            if (prop.type === 'file') {
              prop.value.id = prop.id
              item[prop.name] = item[prop.name] ? [...item[prop.name], prop.value] : [prop.value]
            } else {
              item[prop.name] = prop.value.value
            }
          })
          item.historyVersions.forEach(ver => {
            ver.properties.forEach(prop => {
              if (prop.type === 'file') {
                prop.value.id = prop.id
                ver[prop.name] = ver[prop.name] ? [...ver[prop.name], prop.value] : [prop.value]
              } else {
                ver[prop.name] = prop.value.value
              }
            })
          })
        })
        this.drawingsData = data.page.results
        this.total = data.page.total
      }).catch(err => {
        err.message === 'cancel' || (this.tableIsLoading = false)
        ErrorHandler.formatError(err)
      })
    },
    viewDigitalDrawing (row, isSingleDrawing) {
      if (isSingleDrawing) {
        store.state.isPreviewDrawingsVersion = true
        store.state.isShowDrawingHistory = false
      }
      oneDigitalDrawingsApi.getPidParseInfo(row.projectSpaceId, row.id).then(({ data }) => {
        this.isPreView = true
        data.aimsDocuments.forEach(item => {
          item.repositoryId = data.repoId
          item.version = data.versionId
        })
        store.state.pidParseSuccess = [data]
        store.state.isPreView = this.isPreView
        this.hideOrShowCurrentSceneDom('#digital_drawings_view', 'show')
        this.hideOrShowPidToolBar('hide')
      }).catch(err => {
        ErrorHandler.formatError(err)
      })
    },

    handleClickFile (file, row) {
      const { id, fileName, fileSuffix, size } = file
      this.filePreviewFlag = true
      const fileDownloadUrl = `/projectSpaces/${row.projectSpaceId}/pids/attachment/${id}/download`
      this.$nextTick(() => {
        this.$refs.filePreview.addPreviewFile({ fileName, fileSuffix, size, fileDownloadUrl, id })
      })
    },
    formatterTime (val) {
      return FormatUtil.formatterTime(val)
    },
    goBack () {
      if (this.isPreviewDrawingsVersion && !this.isPreView) {
        store.state.isPreviewDrawingsVersion = false
        return
      }
      this.isFullScreen && document.exitFullscreen()
      this.isPreView = false
      store.state.isPreView = this.isPreView
      this.hideOrShowCurrentSceneDom('#digital_drawings_view', 'hide')
    },
    handleFilterChange (allFilter, title) {
      allFilter.forEach(({ title, checkedValues }) => {
        if (this.showColumns.findIndex(col => col.name === title) > -1) {
          this.$set(this.filterAndValue.propNameToValues, this.titleToParamKey.get(title), checkedValues)
          this.debounce()
        } else {
          this.$set(this.filterAndValue, this.titleToParamKey.get(title), checkedValues)
        }
      })
    },
    search () {
      this.currentPage = 1
      this.debounce()
    },
    sortChange (column) {
      const order = column.order ? column.order === 'ascending' ? 'ASC' : 'DESC' : null
      if (column.prop === 'updateTime') {
        this.$set(this.filterAndValue, 'updateTimeOrder', order)
        this.$set(this.filterAndValue, 'nameOrder', null)
      } else {
        this.$set(this.filterAndValue, 'nameOrder', order)
        this.$set(this.filterAndValue, 'updateTimeOrder', null)
      }
      this.debounce()
    },
    handleSizeChange (size) {
      this.pageSize = size
      this.debounce()
    },
    handleCurrentChange (currentPage) {
      this.currentPage = currentPage
      this.debounce()
    },
    filterHighLight (value) {
      return TextTool.highlightFilters(value, this.searchText)
    },
    rowClassName ({ row }) {
      return 'rowId' + row.id
    },
    closeDrawingHistoryDialog () {
      store.state.isShowDrawingHistory = false
    },

    hideOrShowPidToolBar (operation) {
      const scene = FingerUtil.getCurrentScene()
      const LabelToolBarId = 'position'
      const DrawingHistoryToolBarId = 'drawing-history'
      const ExportPdfToolBarId = 'export-pdf'
      const EditDrawing = 'edit-drawing'
      const toolBarIds = [LabelToolBarId, DrawingHistoryToolBarId, ExportPdfToolBarId, EditDrawing]

      let isSuccess = true
      for (let i = 0; i < toolBarIds.length; i++) {
        const $item = scene.$content.find('.pid-toolbar-item').find('.' + toolBarIds[i])
        if ($item && $item.length > 0) {
          const $parent = $item.parent().parent()
          operation === 'hide' ? $parent.hide() : $parent.show()
        } else {
          isSuccess = false
        }
      }

      // 处理工具栏来不及加载的情况
      if (!isSuccess) {
        for (let i = 0; i < toolBarIds.length; i++) {
          let index = 0
          const inter = setInterval(() => {
            const $item = scene.$content.find('.pid-toolbar-item').find('.' + toolBarIds[i])
            if ($item && $item.length > 0 && index < 20) {
              const $parent = $item.parent().parent()
              operation === 'hide' ? $parent.hide() : $parent.show()
              clearInterval(inter)
            }
            index++
          }, 500)
        }
      }
    },

    hideOrShowCurrentSceneDom (domIdOrClassName, operation) {
      const scene = FingerUtil.getCurrentScene()
      const $dom = scene.$content.find(domIdOrClassName)
      if ($dom) {
        operation === 'hide' ? $dom.hide() : $dom.show()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.drawingsTable ::v-deep .el-table__expand-column {
    border-right: none;
}

.go_back {
    position: fixed;
    width: 100%;
    line-height: 40px;
    background-color: rgba(255, 255, 255, 0.75);
    backdrop-filter: blur(4px);
    box-sizing: border-box;
    z-index: 5;
    top: 48px;

    .go_back_btn {
        width: fit-content;
        height: 40px;
        line-height: 56px;
        color: #182A4E;
        font-size: 14px;
        cursor: pointer;
        padding: 0 12px;
    }

    &.go-back-fullscreen {
        left: 0;
        top: 0;
    }
}

.el-table ::v-deep .drawing-name div.cell {
    display: flex;
    align-items: center;
}

.el-table .el-dropdown {
    vertical-align: bottom;

    > button {
        padding: 0;
        border: none;
        background-color: transparent !important;
    }
}

.table_dropdown {
    border-radius: 2px;
    padding: 0;
    left: unset !important;
    right: 16px;
    margin-top: 0 !important;

    li {
        color: #182A4E;
        height: 44px;
        line-height: 44px;
        padding: 0 16px;
        min-width: 88px;
        box-sizing: border-box;

        &:hover {
            color: #182A4E;
            background-color: #eff2f4;
        }

        &.is-disabled {
            opacity: 0.4;
        }
    }

    > ::v-deep .popper__arrow {
        display: none;
    }
}

.attachments {
    margin: 8px 0;

    > * {
        display: block;
        width: fit-content;
    }
}
</style>
