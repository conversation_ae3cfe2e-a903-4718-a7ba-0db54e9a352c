<template>
    <div style="position: relative" :style="{height: divHeight}">
        <el-table
            :id="rowTable.id"
            stripe
            border
            auto-fit-column
            highlight-current-row
            :row-key="rowTable.id"
            :data="rowTable.historyVersions"
            :max-height="detailTableHeight"
            :row-class-name="rowClassName"
            :default-sort="{prop: 'updateTime', order: 'descending'}"
            :header-cell-style="{ background: '#EFEFEF' }"
            :style="{width: tableWidth + 'px !important'}"
            style="position: sticky; left: 16px">
            <el-table-column
                show-overflow-tooltip
                sortable
                prop="name"
                :label="isSingleDrawing ?  '图纸文件' : '历史版本图纸文件'">
                <template v-slot="{ row }">
                    <el-link
                        type="primary"
                        :underline="false"
                        :disabled="row.parseStatus!==2"
                        v-html="filterHighLight(row.name + '.' + row.suffix)"
                        @click="viewDigitalDrawing(row)">
                    </el-link>
                </template>
            </el-table-column>
            <el-table-column
                show-overflow-tooltip
                v-for="item in showColumns"
                :width="item.type === 'file' ? 260 : 200"
                :key="item.id"
                :prop="item.name"
                :label="item.name">
                <template v-slot="{ row }">
                    <div v-if="item.type !== 'file'">
                        <span v-if="row[item.name] && item.type !== 'select'"
                              v-html="filterHighLight(row[item.name])"></span>
                        <span v-else-if="row[item.name] && item.type === 'select'">{{ row[item.name] }}</span>
                        <span v-else>-</span>
                    </div>
                    <div v-else>
                        <div v-if="row[item.name] && row[item.name].length" class="attachments">
                            <el-link
                                v-for="attachment in row[item.name]"
                                :key="attachment.id"
                                type="primary"
                                :underline="false"
                                v-html="filterHighLight(attachment.fileName + '.' + attachment.fileSuffix)"
                                @click="handleClickFile(attachment, row)">
                            </el-link>
                        </div>
                        <span v-else>-</span>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                show-overflow-tooltip
                prop="updateUserName"
                label="更新人">
                <template v-slot="{ row }">
                    <span v-if="row.updateUserName" v-html="filterHighLight(row.updateUserName)"></span>
                    <span v-else>-</span>
                </template>
            </el-table-column>
            <el-table-column
                show-overflow-tooltip
                prop="updateTime"
                label="更新时间"
                sortable>
                <template v-slot="{ row }">
                    <span v-if="row.updateTime">{{ formatterTime(row.updateTime) }}</span>
                    <span v-else>-</span>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
import FormatUtil from '@/utils/FormatUtil'
import TextTool from '@/utils/TextTool'

export default {
  name: 'HistoricalVersionTable',
  props: {
    rowTable: {
      type: Object,
      required: true
    },
    searchText: {
      type: String,
      required: false,
      default: ''
    },
    showColumns: {
      type: Array,
      required: true
    },
    isSingleDrawing: {
      type: Boolean,
      required: false
    }
  },
  computed: {
    detailTableHeight () {
      return '440px'
    },
    tableWidth () {
      return document.getElementById('draw_parent_table').clientWidth - 36
    },
    divHeight () {
      return this.$nextTick(() => {
        return document.getElementById(this.rowTable.id).clientHeight + 'px'
      })
    }
  },
  methods: {
    viewDigitalDrawing (row) {
      this.$emit('viewDigitalDrawing', row, this.isSingleDrawing)
    },
    handleClickFile (file, row) {
      this.$emit('handleClickFile', file, row)
    },
    formatterTime (val) {
      return FormatUtil.formatterTime(val)
    },
    filterHighLight (value) {
      return TextTool.highlightFilters(value, this.searchText)
    },
    rowClassName ({ row }) {
      return 'rowId' + row.id
    }
  }
}
</script>

<style scoped lang="scss">
.attachments {
    margin: 8px 0;

    > * {
        display: block;
        width: fit-content;
    }
}
</style>
