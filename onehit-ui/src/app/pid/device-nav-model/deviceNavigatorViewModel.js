/*
 * @Descriptin:
 * @Version: --
 * @Autor: yangkai
 * @Date: 2022-10-13 17:17:34
 * @LastEditors: yangkai
 * @LastEditTime: 2023-01-16 16:15:36
 */
import {modelManager} from 'finger';
import Common from '@/utils/common.js';
import { Api } from '@/api';
import { store } from "@/store";
/**
 * ViewModel
 * @class
 */
export default class DeviceNavigatorViewModel {
	/**
	 *
	 * @param options
	 */
	constructor(model) {
		this.model = model;
	}

	async onClick(event, treeNode, currentModel) {
		if (!event._index) {
			return;
		}
		this.treeNodeId = event.id;
		const repoModels = await modelManager.request('RepoModel');
		const repository = event._index.substr(0, event._index.indexOf('_'));
		let curModel = repoModels.find(repoModel => repoModel.repositoryId === repository);
		const itemModel = await curModel.requestItemModel(event._id);
		itemModel.documentId = event.documentId;
		const scene = Common.getCurrentScene();
		const config = store.state.config.deviceNavigator;
		const options = {
			scale: Number(config.scale),
		};
		scene.state.set('currentModel', [itemModel], options);
	}

	async onCheck(treeNode) {
		const viewPid = Common.getCurrentViewPID();
		const leafNodesHandles = this.getAllLeafNodesHandles(treeNode);
		if(leafNodesHandles.length > 0) { // handles.length > 0 做判断是因为changeVisibility传[]时会报错。
			console.log("选中的handles", leafNodesHandles)
			viewPid.changeVisible(leafNodesHandles, treeNode.checked);
		}
	}

	// 获取选中节点的handles数组
	getAllLeafNodesHandles(treeNode) {
		let leafNodesHandles = [];
		if (!treeNode.isParent) {
			leafNodesHandles.push(...treeNode.handles);
		} else {
			const children = treeNode.children;
			for (let i = 0; i < children.length; i++) {
				const childNode = children[i];
				if (!childNode.isParent) {
					// 当前节点是叶子节点
					leafNodesHandles.push(...childNode.handles);
				} else {
					// 当前节点是父节点，递归获取子节点的叶子节点
					const childLeafNodes = this.getAllLeafNodesHandles(childNode);
					leafNodesHandles = leafNodesHandles.concat(childLeafNodes);
				}
			}
		}

		return leafNodesHandles;
	}

	async getNodes() {
		const rootNode = {
			id: 'Device',
			name: '分类',
			checked: true,
			isRoot: true,
			// nocheck: true,
		};
		return rootNode;
	}
	findNode() {
		return this.treeNodeId;
	}
}
