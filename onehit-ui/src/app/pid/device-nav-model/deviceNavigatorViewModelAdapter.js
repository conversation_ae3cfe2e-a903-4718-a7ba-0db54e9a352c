/*
 * @Descriptin:
 * @Version: --
 * @Autor: yangkai
 * @Date: 2022-10-13 17:17:34
 * @LastEditors: yangkai
 * @LastEditTime: 2023-01-16 16:15:39
 */
import DeviceNavigatorViewModel from './deviceNavigatorViewModel';

 class DeviceNavigatorViewModelAdapter {
	getModelType() {
		return 'DeviceNavigatorModel';
	}
	getViewModelType() {
		return 'NavigatorView';
	}
	adapt(originModel, options) {
		if (originModel.getType() === this.getModelType()) {
			return new DeviceNavigatorViewModel(originModel, options);
		}
	}
}

export const deviceNavigatorViewModelAdapter = new DeviceNavigatorViewModelAdapter()