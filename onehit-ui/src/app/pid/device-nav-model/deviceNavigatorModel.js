/*
 * @Descriptin:
 * @Version: --
 * @Autor: yangkai
 * @Date: 2022-10-13 17:17:34
 * @LastEditors: yangkai
 * @LastEditTime: 2023-01-16 16:15:30
 */
import Common from "@/utils/common";
import _ from "loadsh";

/**
 * @class
 */
export default class DeviceNavigatorModel {
	constructor() {
		this.type = 'DeviceNavigatorModel';
		this.id = 'Device';
	}

	getType() {
		return this.type;
	}

	selectTreeNodeByKeyword(pageSize, pageNum, keyword){
		const views = Common.getCurrentSceneViews();
		const navigatorView = views.find(view => view.getType() === 'NavigatorView' && view.viewName === 'deviceNavigator');
		const result = navigatorView.treeObj.getNodesByParamFuzzy("name", keyword)
		console.log("result", result);
		const pageResult = _.chunk(result, pageSize);
		if (pageResult.length + 1 >= pageNum) {
			return {
				pageNum: pageNum,
				pageSize: pageSize,
				pages: pageResult.length,
				results: pageResult.length === 0 ? [] : pageResult[pageNum - 1],
				size: pageSize,
				total: result.length
			}
		}
		return {
			pageNum: pageResult.length + 1,
			pageSize: pageSize,
			pages: pageResult.length,
			results: [],
			size: pageSize,
			total: result.length
		};
	}
}
