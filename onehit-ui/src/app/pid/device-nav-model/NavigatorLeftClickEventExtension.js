import $ from 'jquery';
import {globalState, sceneManager} from 'finger';
import Vue from "vue";
import Common from '@/utils/common.js';
import { Api } from '@/api';
import { store } from '@/store'
import _ from 'lodash';
import DocNavigatorLeftPickExt from "@/app/extension/pid/DocNavigatorLeftPickExt";
import bus from "@/utils/bus";

class NavigatorLeftClickEventExtension extends DocNavigatorLeftPickExt{
	constructor() {
		super();
		this.isInitWatch = false;
		this.currentDocumentId = null;
		if (!sceneManager.getCurrentScene()) return;
		// 放一个空数据的vue组件在这里
		const timer = setInterval(() => {
			const scene = sceneManager.getCurrentScene();
			if (scene) {
				const views = scene.getViews();
				const navigatorView = views.find(v => v.getType() === 'NavigatorView' && v.viewName === 'deviceNavigator');
				if (navigatorView && navigatorView.navigationTreeID && $(`#${navigatorView.navigationTreeID}`).length > 0) {
					const treeNodeTop = $(`#${navigatorView.navigationTreeID}`).parent().parent();
					const vueContainer = document.createElement('div');
					treeNodeTop.prepend(vueContainer);
					new Vue({
						render: (h) => h()
					}).$mount(vueContainer);
					clearInterval(timer);
				}
			}
		}, 500);

		this.isFirstTimeVisit = true;
		const _this = this;
		this.initWatch();
		globalState && globalState.watch("repoModelAmount", ()=>{
			sceneManager.watchCurrentScene(async function (scene) {
				const execPlatforms = ["OneHit-BusinessWorkbench"];
				if(globalState && execPlatforms.includes(globalState.get('platform')) && scene.name === '数字图纸' && _this.isFirstTimeVisit){
					_this.isFirstTimeVisit = false;
					const views = Common.getCurrentSceneViews();
					const navigatorView = views.find(view => view.getType() === 'NavigatorView' && view.viewName === 'deviceNavigator');
					if (navigatorView && navigatorView.treeObj) {
						const nodes = navigatorView.treeObj.getNodes();
						// 放一个空数据的vue组件在这里
						navigatorView.treeObj.removeNode(nodes[0], true);
						const timer = setInterval(() => {
							const views = sceneManager.getCurrentScene().getViews();
							const navigatorView = views.find(v => v.getType() === 'NavigatorView' && v.viewName === 'deviceNavigator');
							if (navigatorView && navigatorView.navigationTreeID && $(`#${navigatorView.navigationTreeID}`).length > 0) {
								const treeNodeTop = $(`#${navigatorView.navigationTreeID}`).parent().parent();
								const vueContainer = document.createElement('div');
								treeNodeTop.prepend(vueContainer);
								new Vue({
									render: (h) => h()
								}).$mount(vueContainer);
								clearInterval(timer);
								navigatorView.treeObj.addNodes(null, {
									id: 'Device',
									name: '分类',
									checked: true,
									isRoot: true
								});
							}
						}, 500);
					}
				}
			});
		});
		sceneManager.watchCurrentScene(async function (scene) {
			const res = store.state.config.deviceNavigator;
			if (res.PidSceneName.includes(scene.name) && !_this.isInitWatch) {
				_this.watchCurrentModel();
			}
		});
	}
	initWatch() {
		bus.$on('DocPreViewUpdateContent', (documentModel, options) => {
			const execPlatforms = ["OneHit-BusinessWorkbench"];
			if (globalState && execPlatforms.includes(globalState.get('platform'))) {
				const views = Common.getCurrentSceneViews();
				const navigatorView = views.find(v => v.getType() === 'NavigatorView' && v.viewName === 'deviceNavigator');
				if (navigatorView && navigatorView.navigationTreeID) {
					navigatorView.treeObj.checkAllNodes(true);
					navigatorView.treeObj.cancelSelectedNode();
					navigatorView.treeObj.expandAll(false);
					const rootNodes = navigatorView.treeObj.getNodes();
					for (let i = 0; i < rootNodes.length; i++) {
						navigatorView.treeObj.expandNode(rootNodes[i], true);
					}
				}
			}
		});
	}
	checkHandleCondition() {
		const execPlatforms = ["OneHit-BusinessWorkbench"];
		if (globalState && !execPlatforms.includes(globalState.get("platform"))) {
			return false;
		}
		const views = Common.getCurrentSceneViews();
		// 切换图纸清空设备目录搜索内容 start
		const deviceView = views.find(v => v.getType() === 'NavigatorView' && v.viewName === 'deviceNavigator');
		if (deviceView.divUUID) {
			const clearSearch  = document.getElementById(deviceView.divUUID).getElementsByClassName("el-input__suffix");
			clearSearch[0].click();
		}
		// 切换图纸清空设备目录搜索内容 end
		const documentPreviewView = _.find(views, function (v) {
			return v.getType() === 'DocumentPreviewView';
		});
		const NavigatorPIDViewModel = _.find(views, (view) => {
			return view.displayType && view.displayType === 'NavigatorPIDViewModel';
		});
		return documentPreviewView || NavigatorPIDViewModel;
	}
	async handleEvent(arg1, arg2) {
		const views = Common.getCurrentSceneViews();
		// 切换图纸清空设备目录搜索内容 start
		const deviceView = views.find(v => v.getType() === 'NavigatorView' && v.viewName === 'deviceNavigator');
		if (deviceView.divUUID) {
			const clearSearch  = document.getElementById(deviceView.divUUID).getElementsByClassName("el-input__suffix");
			clearSearch[0].click();
		}
		// 切换图纸清空设备目录搜索内容 end
		const documentPreviewView = _.find(views, function (v) {
			return v.getType() === 'DocumentPreviewView';
		});
		const that = this;
		if (documentPreviewView.documentModel && documentPreviewView.documentModel.itemId) {
			return await that.initFilterHandles(documentPreviewView.documentModel, documentPreviewView.documentModel.itemId);
		}
		else {
			return await that.initFilterHandles(arg1);
		}
	}
	/**
	 * 通过配置文件筛选查询结果并生成树节点
	 * @param {Array} itemList - ES查询结果
	 * @param {string} _documentId - 文档ID
	 * @param {string} _name - 配置文件中设置的属性名
	 * @returns {treeNode} 目录树节点
	 */
	createTreeNode(itemList, _documentId, _name) {
		const categoryNode = {};
		const treeNode = [];
		for (let i = 0; i < itemList.length; i++) {
			const item = itemList[i];
			item.id = genUUID();
			item.checked = true;
			item.documentId = _documentId;
			if (item._source[_name]) {
				item.name = item._source[_name];
			} else if (item._source.properties && item._source.properties[_name]) {
				item.name = item._source.properties[_name].value;
			} else {
				item.name = '元件';
			}
			if (!categoryNode[item.category]) {
				if (item.mediumCategories) {
					let mediumCategories = {
						name: item.mediumCategories,
						id: genUUID(),
						checked: true,
						children: [],
					};
					let largeCategoriesIndex = _.findIndex(treeNode, { name: item.largeCategory });
					let mediumCategoriesIndex = null;
					if (largeCategoriesIndex === -1) {
						treeNode.push({
							name: item.largeCategory,
							id: genUUID(),
							checked: true,
							children: [mediumCategories],
						});
					} else {
						mediumCategoriesIndex = _.findIndex(treeNode[largeCategoriesIndex].children, {
							name: item.mediumCategories,
						});
						if (mediumCategoriesIndex === -1) {
							treeNode[largeCategoriesIndex].children.push(mediumCategories);
						}
					}
					largeCategoriesIndex = _.findIndex(treeNode, { name: item.largeCategory });
					mediumCategoriesIndex = _.findIndex(treeNode[largeCategoriesIndex].children, {
						name: item.mediumCategories,
					});
					if (item.smallCategories) {
						let smallCategories = {
							name: item.smallCategories,
							id: genUUID(),
							checked: true,
							children: [],
						};
						treeNode[largeCategoriesIndex].children[mediumCategoriesIndex].children.push(smallCategories);
						let smallCategoriesIndex = _.findIndex(
							treeNode[largeCategoriesIndex].children[mediumCategoriesIndex].children,
							{ name: item.smallCategories },
						);
						treeNode[largeCategoriesIndex].children[mediumCategoriesIndex].children[
							smallCategoriesIndex
						].children.push(item);
					} else {
						treeNode[largeCategoriesIndex].children[mediumCategoriesIndex].children.push(item);
					}
				} else {
					if (item.smallCategories) {
						let smallCategories = {
							name: item.smallCategories,
							id: genUUID(),
							checked: true,
							children: [],
						};
						let largeCategoriesIndex = _.findIndex(treeNode, { name: item.largeCategory });
						if (largeCategoriesIndex === -1) {
							treeNode.push({
								name: item.largeCategory,
								id: genUUID(),
								checked: true,
								children: [smallCategories],
							});
						} else {
							treeNode[largeCategoriesIndex].children.push(smallCategories);
						}
						largeCategoriesIndex = _.findIndex(treeNode, { name: item.largeCategory });
						let smallCategoriesIndex = _.findIndex(treeNode[largeCategoriesIndex].children, {
							name: item.smallCategories,
						});
						treeNode[largeCategoriesIndex].children[smallCategoriesIndex].children.push(item);
					} else {
						treeNode.push({
							name: item.largeCategory,
							id: genUUID(),
							checked: true,
							children: [item],
						});
					}
				}
				categoryNode[item.category] = item;
			} else {
				for (let j = 0; j < treeNode.length; j++) {
					let currentTreeNode = treeNode[j];
					if (!item.mediumCategories && !item.smallCategories) {
						if (currentTreeNode.name === item.largeCategory) {
							currentTreeNode.children.push(item);
							currentTreeNode.children.sort(sortDrawingFun);
							break;
						}
					} else if (item.mediumCategories && !item.smallCategories) {
						if (currentTreeNode.name === item.largeCategory) {
							let mediumCategoriesIndex = _.findIndex(currentTreeNode.children, {
								name: item.mediumCategories,
							});
							currentTreeNode.children[mediumCategoriesIndex].children.push(item);
							currentTreeNode.children[mediumCategoriesIndex].children.sort(sortDrawingFun);
							break;
						}
					} else if (item.mediumCategories && item.smallCategories) {
						if (currentTreeNode.name === item.largeCategory) {
							let mediumCategoriesIndex = _.findIndex(currentTreeNode.children, {
								name: item.mediumCategories,
							});
							let smallCategoriesIndex = _.findIndex(
								currentTreeNode.children[mediumCategoriesIndex].children,
								{ name: item.smallCategories },
							);
							currentTreeNode.children[mediumCategoriesIndex].children[
								smallCategoriesIndex
							].children.push(item);
							currentTreeNode.children[mediumCategoriesIndex].children[
								smallCategoriesIndex
							].children.sort(sortDrawingFun);
							break;
						}
					} else if (!item.mediumCategories && item.smallCategories) {
						if (currentTreeNode.name === item.largeCategory) {
							let smallCategoriesIndex = _.findIndex(currentTreeNode.children, {
								name: item.smallCategories,
							});
							currentTreeNode.children[smallCategoriesIndex].children.push(item);
							currentTreeNode.children[smallCategoriesIndex].children.sort(sortDrawingFun);
							break;
						}
					}
				}
			}
		}
		treeNode.forEach(category => {
			category.children.forEach(child => {
				child.route = "/分类/" +category.name + "/"
			})
		})
		return treeNode;
	}
	/**
	 * 添加对当前model的监听
	 */
	watchCurrentModel() {
		const that = this;
		const scene = sceneManager.getCurrentScene();
		scene.state.watch('currentModel', async (preState, newStates, options) => {
			// 存在 setCurrentModel为null的情况
			if (!newStates){
				return;
			}
			if (
				newStates[0] &&
				newStates[0].documentId &&
				that.currentDocumentId !== newStates[0].documentId &&
				that.isInitWatch
			) {
				setTimeout(async () => {
					that.initFilterHandles(newStates[0], newStates[0].itemId);
				}, 1000);
			} else {
				if (newStates.length) {
					const navigatorView = _.find(scene.getViews(), function (v) {
						return v.getType() === 'NavigatorView' && v.viewName === 'deviceNavigator';
					});
					if (navigatorView && navigatorView.treeObj) {
						const treeNodes = navigatorView.treeObj.getNodesByParam('_id', newStates[0].itemId);
						navigatorView.treeObj.selectNode(treeNodes[0], false, true);
						if ($(`#${navigatorView.navigationTreeID}`).find('.curSelectedNode')[0] && options) {
							let treeNodeTop = $(`#${navigatorView.navigationTreeID}`).find('.curSelectedNode')[0]
								.offsetTop;
							const treeBoxHeight = $(`#${navigatorView.navigationTreeID}`)
								.parents('.navigationTreeBox')
								.height();
							let scrollTop = treeNodeTop - treeBoxHeight;
							if (scrollTop >= 0) {
								$(`#${navigatorView.navigationTreeID}`)
									.parents('.navigationTreeBox')
									.scrollTop(scrollTop + treeBoxHeight * 0.6);
							} else {
								$(`#${navigatorView.navigationTreeID}`)
									.parents('.navigationTreeBox')
									.scrollTop(scrollTop);
							}
						}
					}
				}
			}
		});
		that.isInitWatch = true;
	}
	/**
	 * 初始化筛选handles
	 * @param {String} params.repositoryId - 仓库Id
	 * @param {String} params.documentId   - 文档Id
	 */
	async initFilterHandles(params, itemId) {
		const scene = sceneManager.getCurrentScene();
		const views = scene.getViews();
		this.currentDocumentId = params.documentId;
		let formData = new FormData();
		formData.append('documentIds', JSON.stringify([params.documentId]));
		let handlesList = null;
		try {
			const { data } = await Api.getReferences2(params.repositoryId, formData);
			handlesList = data;
		} catch (error) {
			console.log(error);
			return;
		}
		handlesList = handlesList.filter((item) => item.targetItemId);
		let searchResults = [],
			idsList = [];
		for (let i = 0; i < handlesList.length; i++) {
			idsList.push(handlesList[i].targetItemId);
		}

		let formData2 = new FormData();
		formData2.append('sourceDocumentId', params.documentId);
		formData2.append('targetItemIds', JSON.stringify(idsList));
		const { data } = await Api.getReferences2(params.repositoryId, formData2);
		handlesList = data;
		console.log("handlesList", handlesList)

		const filter = {
			query: {
				ids: {
					type: '_doc',
					values: idsList,
				},
			},
			from: 0,
			size: 9999,
		};
		let resultList = null;
		try {
			const { data } = await Api.getCategoryFilterResults(params.repositoryId, filter);
			resultList = data;
		} catch (error) {
			console.log(error);
			return;
		}
		searchResults = searchResults.concat(resultList.hits.hits);
		const scrollId = resultList._scroll_id;
		const count = Math.ceil(resultList.hits.total / 9999);
		for (let i = 0; i < count; i++) {
			if (searchResults.length < resultList.hits.total) {
				let newResultList = null;
				try {
					const { data } = await Api.getCategoryFilterResultsByScroll(scrollId);
					newResultList = data;
				} catch (error) {
					console.log(error);
					return;
				}
				searchResults = searchResults.concat(newResultList.hits.hits);
			} else {
				break;
			}
		}
		const config = store.state.config.deviceNavigator;
		const categoryfilterList = searchResults.filter((item) => {
			let hasCategory = false;
			item._source.categories.forEach((cate) => {
				if (config.largeCategories.indexOf(cate) !== -1) {
					item.largeCategory = cate;
					item.category = cate;
					hasCategory = true;
				}
				if (config.mediumCategories.indexOf(cate) !== -1) {
					item.mediumCategories = cate;
					item.category = cate;
				}
				if (config.smallCategories.indexOf(cate) !== -1) {
					item.smallCategories = cate;
					item.category = cate;
				}
			});
			const itemHandles = handlesList.find(function(obj) {return obj.targetItemId === item._id;})
			item.handles = itemHandles ? itemHandles.handles : []
			return hasCategory;
		});
		const navigatorView = _.find(views, function (v) {
			return v.getType() === 'NavigatorView' && v.viewName === 'deviceNavigator';
		});
		if (navigatorView && navigatorView.treeObj) {
			const rootNode = navigatorView.treeObj.getNodes();
			const categoryTreeNode = this.createTreeNode(categoryfilterList, params.documentId, config.name);
			navigatorView.treeObj.removeChildNodes(rootNode[0]);
			if (categoryTreeNode.length) {
				navigatorView.treeObj.addNodes(rootNode[0], categoryTreeNode);
                if (itemId) {
					setTimeout(()=>{
						const treeNodes = navigatorView.treeObj.getNodesByParam('_id', itemId);
						navigatorView.treeObj.selectNode(treeNodes[0], false, false);
					}, 100)
				}
			}
		}
	}
}
/**
 * 对节点进行排序
 */
function sortDrawingFun(a, b) {
	const numberReg = new RegExp(/^\d{1,}$/);
	if (numberReg.test(a.name) || numberReg.test(b.name)) {
		return a.name - b.name;
	} else {
		return a.name.localeCompare(b.name);
	}
}
/**
 * 生成唯一标识
 */
function genUUID() {
	return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
		let r = (Math.random() * 16) | 0,
			v = c === 'x' ? r : (r & 0x3) | 0x8;
		return v.toString(16);
	});
}

export const navigatorLeftClickEventExtension = new NavigatorLeftClickEventExtension();
