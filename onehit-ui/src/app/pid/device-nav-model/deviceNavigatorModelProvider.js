/*
 * @Descriptin:
 * @Version: --
 * @Autor: yangkai
 * @Date: 2022-10-13 17:17:34
 * @LastEditors: yangkai
 * @LastEditTime: 2023-01-16 16:15:34
 */
import DeviceNavigatorModel from './deviceNavigatorModel';

/**
 * @class
 * ObjectAssetModel 的提供者
 */
 class DeviceNavigatorModelProvider {
	constructor() {
		this.modelType = 'DeviceNavigatorModel';
	}

	getModelType() {
		return this.modelType;
	}

	async request(option) {
		const deviceNavigatorModel = new DeviceNavigatorModel();
		return [deviceNavigatorModel];
	}
}

export const deviceNavigatorModelProvider = new DeviceNavigatorModelProvider()