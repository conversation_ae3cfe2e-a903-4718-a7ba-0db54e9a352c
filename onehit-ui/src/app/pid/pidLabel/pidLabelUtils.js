import {modelManager} from "finger";
import $ from "jquery";
import _ from "lodash-oneHit";
import {Message, MessageBox} from "element-ui";
import bus from "../../../utils/bus";
import {Api} from "@/api";
import scenatorUtils from "@/common/scenatorDomUtils";
import Common from "@/utils/common";
import {emitLabelExit} from "@/app/pid/pidLabel/pidLabel-event-Utils";

let message = null;
let pidLabelTypeOptions = [];
const openPidTabs = () => {
    const listTab = scenatorUtils.getTabDom("标注列表");
    const infoTab = scenatorUtils.getTabDom("标注信息");
    if (listTab) {listTab.click();}
    if (infoTab) {infoTab.click();}
};
const closePidTabs = () => {
    scenatorUtils.getSusPendHide().click();
};

const closeToolBar = () => {
    const ele = scenatorUtils.getPidToolBoxElement("标注");
    ele ? ele.children[0].callback.MenuCloseOrOpen.close.callback() : '';
};

const openMousePidStyle = () => {
    const documentPreviewView = Common.getCurrentSceneViews().find((view) => view.getType() === 'DocumentPreviewView');
    const pidType = documentPreviewView.documentviewerBar.currentDocType;
    if(pidType === "svg") {
        $(`#${documentPreviewView.documentviewerBar.svgBar.svgIdRoot}`).addClass("imagelabel-cursor-note");
    }else {
        $(`#${documentPreviewView.documentviewerBar.outerId} #bcd`).addClass("imagelabel-cursor-note");
    }
}

const closeMousePidStyle = () => {
    const documentPreviewView = Common.getCurrentSceneViews().find((view) => view.getType() === 'DocumentPreviewView');
    const pidType = documentPreviewView.documentviewerBar.currentDocType;
    if(pidType === "svg") {
        $(`#${documentPreviewView.documentviewerBar.svgBar.svgIdRoot}`).removeClass("imagelabel-cursor-note");
    }else {
        $(`#${documentPreviewView.documentviewerBar.outerId} #bcd`).removeClass("imagelabel-cursor-note");
    }
}

const locationDocContent = async function (repositoryId, docId, itemId, tag, docName) {
    const RepoModels = await modelManager.request('RepoModel');
    let RepoModel = _.find(RepoModels, {repositoryId: repositoryId});
    if (RepoModel) {
        let itemModel;
        if (tag) {
            try {
                itemModel = await RepoModel.requestItemModelByTag(tag, {}, '', false);
            } catch (error) {
                console.error(error);
            }
        }
        if (!itemModel) {
            try {
                itemModel = await RepoModel.requestItemModel(itemId, {}, '', false);
            } catch (error) {
                console.error(error);
            }
        }
        if (itemModel) {
            itemModel.id = itemModel.itemId;
            const docModels = await RepoModel.requestDOCModelByName(docName);
            if (docModels && docModels.length > 0) {
                itemModel.documentId = docModels[0].id;
            }
            Common.getCurrentScene().state.set('currentModel', [itemModel], {
                scale: 0.2
            });
        }
    }
}

const showMessage = function () {
    message = Message({
        dangerouslyUseHTMLString: true,
        duration: 0,
        customClass: 'scenatorStyle scenator_briefMsg info',
        message: '使用 ESC 键，可退出位置标注',
        onClose: (msg) => {
            message = null;
            emitLabelExit();
        },
    });
}

const closeMessage = function () {
    message ? message.close() : '';
    message = null;
}

const openNewDocumentByDocModel = async function (repositoryId, docId, docName) {
    const RepoModels = await modelManager.request('RepoModel');
    let RepoModel = _.find(RepoModels, {repositoryId: repositoryId});
    if (RepoModel) {
        let DOCModel;
        if (docName) {
            const docModels = await RepoModel.requestDOCModelByName(docName);
            if (docModels && docModels.length !== 0) {
                DOCModel = docModels[0];
            }
        }
        if (!DOCModel) {
            DOCModel = await RepoModel.requestDOCModel(docId);
        }
        if (DOCModel) {
            DOCModel['itemId'] = ' ';
            const documentPreviewView = _.find(Common.getCurrentSceneViews(), (view) => view.getType() === 'DocumentPreviewView');
            documentPreviewView.addModel(DOCModel, {});
            Common.getCurrentScene().state.set('currentModel', [DOCModel]);
        }
    }
}

const screenToWorld = async (point) => {
    const documentPreviewView = Common.getCurrentSceneViews().find((view) => view.getType() === 'DocumentPreviewView');
    const worldPoint = await documentPreviewView.screenToWorld(point);
    return worldPoint.worldPoint;
}

const worldToScreen = async (point) => {
    const documentPreviewView = Common.getCurrentSceneViews().find((view) => view.getType() === 'DocumentPreviewView');
    const screenPoint = await documentPreviewView.worldToScreen(point);
    return screenPoint.screenPoint;
}

const addImageLabel = async (labelClassificationId, point, status = 'default', isScreen, targetDocName) => {
    const views = Common.getCurrentSceneViews();
    const documentPreviewView = views.find((view) => view.getType() === 'DocumentPreviewView');
    if(targetDocName && documentPreviewView.documentModel.name !== targetDocName) {
        return {
            label: ''
        }
    }
    const pidType = documentPreviewView.documentviewerBar.currentDocType;
    const labelType = pidLabelTypeOptions.find((ele) => ele.id === labelClassificationId);
    if(pidType === 'svg' && !isScreen) {
        point = await worldToScreen(point);
    }
    const houzhui = pidType === 'dwg' ? 'dwg' : 'svg';
    const baseUrl = window.location.origin + '/Scenator/resources/apps/OneHit/';
    const baseLocation = labelType.pidDefaultSymbol.indexOf('pidDefault') >= 0 ? 'images/' : 'upload/';
    const pictureName = status === 'default' ? labelType.pidDefaultSymbol.substring(0, labelType.pidDefaultSymbol.lastIndexOf('.')) : labelType.pidSelectedSymbol.substring(0, labelType.pidSelectedSymbol.lastIndexOf('.'))
    let pictureUrl = baseUrl + baseLocation + pictureName + '.' + houzhui;
    let isLoadEnd = pidType === 'dwg';
    let offsetHeight = 0;
    if(pidType === 'svg') {
        point = await screenToWorld(point);
        const img = new Image();
        img.src = pictureUrl;
        img.onload = async function() {
            offsetHeight = this.height;
            point[1] = point[1] - offsetHeight;
            isLoadEnd = true;
        };
        img.onerror = async function() {
            // TODO: bug: 缺失对应的交互
        };
        await new Promise((resolve) => {
            let intervalTimes = 0;
            const interval = setInterval(() => {
                intervalTimes++;
                if(intervalTimes === 100 || isLoadEnd) {
                    clearInterval(interval);
                    resolve();
                }
            }, 50)
        });
        point = await worldToScreen(point);
    }
    const param = {
        labelUrl: pictureUrl,
        point: point,
        isNeedTransformPoint: pidType === 'svg'
    }
    if(targetDocName && documentPreviewView.documentModel.name !== targetDocName) {
        return {
            label: ''
        }
    }
    const label = await documentPreviewView.addImageLabel(param);
    if(!label) {
        // TODO: bug: 缺失对应的交互
    }
    if(pidType === 'svg') {
        param.point = await screenToWorld(point);
        param.point[1] = param.point[1] + offsetHeight;
    }
    param.label = label.label ? label.label : '';
    return param;
}

const removeImageLabel = async (handle) => {
    if(!handle) return;
    const views = Common.getCurrentSceneViews();
    const documentPreviewView = views.find((view) => view.getType() === 'DocumentPreviewView');
    try {
        await documentPreviewView.removeLabel(handle);
    }catch (e) {
        console.log(handle + ":标签移除失败", e)
    }

}

const getLabelTypeList = async () => {
    const { data: res } = await Api.getLabelTypeList({pageSize: 9999});
    pidLabelTypeOptions = res.result.records.filter((item) => item.pidDefaultSymbol || item.pidSelectedSymbol);
}
const watchClickOther = function () {
    // 绑定页面点击事件
    $('#edc_main_element').on('mousedown', (ev) => {
        let currentScene = Common.getCurrentScene();
        // 如果当前在编辑标注  才监听。
        if(message) {
            if ($($('#scene_container ' + '#' + currentScene.$content[0].id + ' #pidLabelInfo')).has($(ev.target)).length > 0) return;
            if ($('.labelclassfication-select').has($(ev.target)).length > 0) return;
            if(!!ev.target.closest('.image-viewer-canvas') || ev.target.nodeName === 'IMG') return;
            MessageBox({
                title: '确认提示',
                customClass: 'videoMsgWindow scenator_confirmPrompt ask',
                closeOnClickModal: false,
                closeOnPressEscape: false,
                showClose: false,
                message: '是否退出当前标注？',
                showConfirmButton: true,
                confirmButtonText: '退出标注',
                showCancelButton: true,
                cancelButtonText: '取消',
                confirmButtonClass: 'el-button el-button--main--button scenatorStyle',
                cancelButtonClass: 'el-button el-button--third--button scenatorStyle',
            }).then(() => {
                closeMessage();
                emitLabelExit();
            })
        }
    });
}

const readyLabel = function () {
    closePidTabs();
    openMousePidStyle();
    showMessage();
    watchClickOther();
}
const endLabel = function () {
    openPidTabs();
    closeMousePidStyle();
    closeMessage();
}
getLabelTypeList();
export default {
    openPidTabs,
    closePidTabs,
    closeToolBar,
    openMousePidStyle,
    closeMousePidStyle,
    locationDocContent,
    showMessage,
    closeMessage,
    watchClickOther,
    openNewDocumentByDocModel,
    screenToWorld,
    worldToScreen,
    addImageLabel,
    removeImageLabel,
    readyLabel,
    endLabel
};
