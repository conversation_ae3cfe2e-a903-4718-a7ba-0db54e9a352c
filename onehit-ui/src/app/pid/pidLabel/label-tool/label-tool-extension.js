import pidLabelUtils from '../pidLabelUtils';
import $ from "jquery";
import store from "@/store";
import {Api} from "@/api";
import {Message} from "element-ui";
import DocPreviewToolBarExt from "@/app/extension/pid/DocPreViewToolBarExt";
import scenatorUtils from "@/common/scenatorDomUtils";
import './label-tool-css.css'
import Common from "@/utils/common";
import Vue from "vue";
import {globalState} from "finger";
class LabelToolExtension extends DocPreviewToolBarExt{

    checkCreateCondition() {
        const execPlatforms = ["OneHit-BusinessWorkbench"];
        if (globalState && !execPlatforms.includes(globalState.get("platform"))) {
            return false;
        }
        const {label: config} = store.state.config;
        const currentScene = Common.getCurrentScene();
        const pidScene = config.PidSceneName;
        return pidScene.includes(currentScene.name)
    }
    create() {
        super.create();
        const menu1 = {
            name:"标注",
            title:"标注",
            icon:"position",
            orderIndex:71,
            type:"CloseOrOpen",
            callback:{
                close:{
                    icon:"position",
                    callback:function(myself, outerBar, element) {}
                },
                open:{
                    icon:"position-hover",
                    callback:function(myself, outerBar, element){}
                }
            },
            children:[{
                title: "添加标注",
                tooltip: "添加标注",
                disabled: false,
                type: "MenuCloseOrOpen",
                callback: {
                    MenuCloseOrOpen: {
                        open: {
                            callback: async function (myself, outerBar) {
                                const views = Common.getCurrentSceneViews();
                                const documentPreviewView = Common.getCurrentSceneViews().find((view) => view.getType() === 'DocumentPreviewView');
                                // 已经有打开的功能时则自动关闭
                                if (documentPreviewView){
                                    documentPreviewView.closeToolbar("Cloud")
                                }
                                const pidLabelTypeOptions = views.find((view) => view.getType() === 'PidLabelInfoView').vm.$children[0].pidLabelTypeOptions;
                                if(pidLabelTypeOptions.length <= 0) {
                                    Vue.prototype.$msgbox({
                                        title: '信息提示',
                                        customClass: "scenator_confirmPrompt info",
                                        closeOnClickModal: false,
                                        closeOnPressEscape: false,
                                        showClose: false,
                                        message: `请在配置管理中添加P&ID标注后，再使用此功能。`,
                                        showConfirmButton: true,
                                        confirmButtonText: '确定',
                                        showCancelButton: false,
                                        cancelButtonText: '确定',
                                        confirmButtonClass: 'el-button el-button--main--button less3word',
                                        cancelButtonClass: 'el-button el-button--secondary--button less3word'
                                    }).then(action => {
                                        pidLabelUtils.closeToolBar();
                                    });
                                    return;
                                }
                                if(!documentPreviewView){
                                    return;
                                }
                                pidLabelUtils.readyLabel();
                            }
                        },
                        close: {
                            callback: function (myself) {
                                const documentPreviewView = Common.getCurrentSceneViews().find((view) => view.getType() === 'DocumentPreviewView');
                                if(!documentPreviewView){
                                    return;
                                }
                                pidLabelUtils.endLabel();
                                const ele = scenatorUtils.getPidToolBoxElement("标注");
                                ele.stopElement();
                            }
                        }
                    }
                }
            },
                {
                    title: "导出标注信息",
                    icon: 'export',
                    tooltip: "导出标注信息",
                    disabled: false,
                    type: "PlainClick",
                    callback: {
                        onclick: {
                            callback: (myself, outerBar) => {
                                this.exportLabels();
                            }
                        }
                    }
                }]
        }
        return [menu1]
    }

    async exportLabels() {
        Api.exportLabels().then((res) => {
            let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1];
            const blob = new Blob([res.data], { type: 'application/xls' });
            const url = window.URL.createObjectURL(blob);
            const fileDownloader = document.createElement('a');
            fileDownloader.href = url;
            fileDownloader.id = 'fileDown';
            fileDownloader.download = `${name}`;
            fileDownloader.click();
            Message({
                dangerouslyUseHTMLString: true,
                duration: 3000,
                customClass: 'scenatorStyle scenator_briefMsg success',
                message: '您的文件导出成功！',
            });
            $('.pid-toolbar-item .export-hover').click();
        }).catch((error => {
            const reader = new FileReader()
            reader.readAsText(error.data, 'utf-8')
            reader.onload = function () {
                const e = reader.result
                const  obj = JSON.parse(e)
                Message({
                    dangerouslyUseHTMLString: true,
                    duration: 3000,
                    customClass: 'scenatorStyle scenator_briefMsg error',
                    message: `${obj?.message === "请检查标注分类是否包含特殊字符"? obj?.message : '您的文件导出失败！'}`,
                });
            }
        }))
    }
}

export const pidToolMenuLabelExtension = new LabelToolExtension();