import pidLabelUtils from '../pidLabelUtils';
import {Api} from "@/api";
import DocPreviewLeftPickExt from "@/app/extension/pid/DocPreViewLeftPickExt";
import scenatorUtils from "@/common/scenatorDomUtils";
import Common from "@/utils/common";
import {emitLabelImageChange, onLabelInfoChange, onLabelListChange} from "@/app/pid/pidLabel/pidLabel-event-Utils";
import {globalState} from "finger";

class PidLeftClickedLabelExtension extends DocPreviewLeftPickExt{
    constructor(){
        super();
        this.pidDefaultLabel = null;
        this.pidLabelTypes = [];
        // 当前高亮的图标
        this.pidCurrentHighLightLabel = null;
        this.pidType = 'dwg';
        this.baseUrl = window.location.origin + '/Scenator/resources/apps/OneHit/';
        this.getLabelTypeList();
        onLabelInfoChange((labelInfo, status) => {
            if((status === "edit" || status === "editImage") && labelInfo && this.pidCurrentHighLightLabel && this.pidCurrentHighLightLabel.id === labelInfo.id) {
                this.pidCurrentHighLightLabel = labelInfo;
            }
        });
        onLabelListChange((labelInfo) => {
            if(labelInfo.status !== 'hide') {
                this.pidCurrentHighLightLabel = labelInfo.node;
            } else {
                this.pidCurrentHighLightLabel = null;
            }
        });
    }

    checkHandleCondition() {
        const execPlatforms = ["OneHit-BusinessWorkbench"];
        if (globalState && !execPlatforms.includes(globalState.get("platform"))) {
            return false;
        }
        const views = Common.getCurrentSceneViews();
        this.pidType = views.find((view) => view.getType() === 'DocumentPreviewView').documentviewerBar.currentDocType;
        const pidLabelInfoView = views.find((view) => view.getType() === 'PidLabelInfoView');
        const actionType = pidLabelInfoView ? pidLabelInfoView.vm.$children[0].actionType : null;
        const canSave = pidLabelInfoView ? pidLabelInfoView.vm.$children[0].canSave : null;
        const toolId = scenatorUtils.getPidToolBoxElement("标注") ? scenatorUtils.getPidToolBoxElement("标注").children[0].id : null;
        return (actionType === 'editPos' && !canSave) || (toolId && document.getElementById(toolId).classList.contains("select") && actionType !== 'add')
    }

    async handleEvent(itemModel, options) {
        await this.updateImageLabel(itemModel, options);
    }

    async updateImageLabel(itemModel, options) {
        const labelClassificationId = this.pidCurrentHighLightLabel ? this.pidCurrentHighLightLabel.labelClassificationId : this.pidDefaultLabel.id;
        const views = Common.getCurrentSceneViews();
        const documentPreviewView = views.find((view) => view.getType() === 'DocumentPreviewView');
        const point = this.pidType === 'svg' ? [options.clientX, options.clientY] : [options.pickMessage.x, options.pickMessage.y];
        const label = await pidLabelUtils.addImageLabel(labelClassificationId, point, "default", true);
        label.labelTypeId = labelClassificationId;
        label.itemIds = itemModel.map(item => item.itemId);
        label.repositoryId = documentPreviewView.documentModel.repositoryId;
        label.tag = itemModel[0] ? itemModel[0].tag : '';
        label.documentId = documentPreviewView.id;
        label.documentName = documentPreviewView.documentModel.name;
        pidLabelUtils.closeMousePidStyle();
        pidLabelUtils.openPidTabs();
        emitLabelImageChange(label);
    }

    async getLabelTypeList() {
        const { data: res } = await Api.getLabelTypeList({pageSize: 9999, queryChild: true});
        this.pidLabelTypes = res.result.records.filter((item) => item.pidDefaultSymbol || item.pidSelectedSymbol);
        this.pidDefaultLabel = res.result.records.find((item) => item.pidDefaultSymbol || item.pidSelectedSymbol);
    }

}

export const pidLeftClickedLabelExtension = new PidLeftClickedLabelExtension();