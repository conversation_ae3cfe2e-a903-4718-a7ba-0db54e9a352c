import DocPreviewLeftPickExt from "@/app/extension/pid/DocPreViewLeftPickExt";
import {emitLabelHighLightChange} from "@/app/pid/pidLabel/pidLabel-event-Utils";
import Common from "@/utils/common";
import scenatorUtils from "@/common/scenatorDomUtils";
import {globalState} from "finger";

class LabelHighLightExtension extends DocPreviewLeftPickExt{
    constructor(){
        super();
    }

    // TODO: 工具栏中有场景判断，这里没有。不配置的话也会执行处理函数。
    checkHandleCondition() {
        const execPlatforms = ["OneHit-BusinessWorkbench"];
        if (globalState && !execPlatforms.includes(globalState.get("platform"))) {
            return false;
        }
        // 如果正在新增或者编辑位置时候，点击了图纸上别的标注则不做任何反应
        const actionType = Common.getCurrentSceneViews().find((view) => view.getType() === 'PidLabelInfoView').vm.$children[0].actionType;
        return actionType !== 'editPos';
    }

    async handleEvent(itemModel, options) {
        emitLabelHighLightChange(options.pickMessage.handles);
    }


}

export const labelHighLightExtension = new LabelHighLightExtension();