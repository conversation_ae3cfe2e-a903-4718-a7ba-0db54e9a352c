import ViewFactory from "@/app/ViewFactory";
import pidLabelList from '@/components/pidLabel/pidLabelList.vue';
import bus from "@/utils/bus";
class PidLabelListViewProvider {
    getViewType() {
        return 'PidLabelListView';
    }
    request(options) {
        const view = ViewFactory.createByApp(pidLabelList, this.getViewType());
        view.showCurrentDocLabel = (docId) => {
            view.vm.$children[0].getLabelList(docId);
        }
        view.clearKeySearch = () => {
            view.vm.$children[0].keyWord = ''
        }
        return view;
    }
}
export const pidLabelListViewProvider = new PidLabelListViewProvider()