import './main.css';
import {handleClickExport} from './handler'
import DocPreviewToolBarExt from "@/app/extension/pid/DocPreViewToolBarExt";
import {globalState} from "finger";

class PIdExportPdfExtension extends DocPreviewToolBarExt {

    checkCreateCondition() {
        const execPlatforms = ["OneHit-BusinessWorkbench"];
        if (globalState && !execPlatforms.includes(globalState.get("platform"))) {
            return false;
        }
        return true;
    }

    create() {
        const element1 = {
            title: "导出为pdf",
            icon: "export-pdf",
            orderIndex: 400, //默认 99，排序规则从小到大，按钮自上而下。
            type: "MouseDownForThreeStyles",
            callback: {
                mouse: {
                    //鼠标按下
                    mousedown: {
                        icon: "mousedown-class"
                    },
                    //鼠标抬起
                    mouseup: {
                        icon: "mouseup-class",
                        callback: handleClickExport
                    }
                }
            }
        };
        return [element1];
    }
}


export const PIdExportPdfBtnExtension = new PIdExportPdfExtension();
