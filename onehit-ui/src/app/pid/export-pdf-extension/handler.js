/* 处理拓展逻辑文件 */
import { Message } from "element-ui";
import { getTepositoryIdAndDocumentId } from '@/app/pid/export-pdf-extension/utils'
import { sceneManager } from "finger";
import { Api } from '@/api';
import { store } from '@/store';
import constants from '@/common/constants.js';

// 处理点击导出的拓展逻辑
const handleClickExport = (o, bar) => {
    const views = sceneManager.getCurrentScene().getViews();

    const documentPreviewView = views.find(v => v.getType() === 'DocumentPreviewView');
    console.log("documentPreviewView", documentPreviewView);

    // 选中节点的时候
    if (documentPreviewView) {
        documentPreviewView.captureScreenShot(async screenShot => {
            // 获取当前树节点选中的节点的信息
            // 判断当前的用户名是否是以1或2开头的
            const isIn = await getUserInfoTo();
            if (isIn) {
                getTepositoryIdAndDocumentId(sceneManager);
            } else {
                messageWarn('没有权限!');
            }

        });
    }
    // 没有节点选中的时候
    else {
        messageWarn('请先选中一张P&ID图纸,再导出为pdf!');
    }
}

const messageWarn = (message) => {
    Message({
        dangerouslyUseHTMLString: true,
        duration: 3000,
        customClass: "scenatorStyle scenator_briefMsg warn",
        message: `<div><article>${message}</article></div>`,
    });
}
// 请求当前用户的信息
const getUserInfoTo = async () => {
    const { exportPDF: config } = store.state.config
    const { data: res } = await Api.getCurrentUser()
    if (!res.roles) {
        return false
    }
    let canExport = false
    res.roles.forEach(role => {
        if (config.authorizedRoles.includes(role.name)) {
            canExport = true
        }
    })
    return canExport
}
// 获取节点的名字
export {
    handleClickExport
}
