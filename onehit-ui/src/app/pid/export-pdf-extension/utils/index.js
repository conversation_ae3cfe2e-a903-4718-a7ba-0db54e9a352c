import {Api} from "@/api";
import {Message} from "element-ui";
import printJS from 'print-js'
//  下载封装1

const downLoad = (res, name, type) => {
    let blob = new Blob([res]);
    let downloadElement = document.createElement("a");
    let href = window.URL.createObjectURL(blob); //创建下载的链接
    downloadElement.href = href;
    downloadElement.download = `${name}.${type}`; //下载后文件名
    document.body.appendChild(downloadElement);
    downloadElement.click(); //点击下载
    document.body.removeChild(downloadElement); //下载完成移除元素
    window.URL.revokeObjectURL(href); //释放掉blob对象
}
// 下载封装2
const download = (response, name, extension) => {
    const {data: buff, contentType: type} = response
    const url = window.URL.createObjectURL(new Blob([buff], {type}))
    const link = document.createElement('a') // 例用a标签的download属性实现下载
    link.style.display = 'none'
    link.href = url
    link.setAttribute('download', `${name || '未命名'}.${extension || type.split('/')[1]}`)
    document.body.appendChild(link) // 在页面中动态添加一个a标签
    link.click()
    document.body.removeChild(link) // 点击之后删除该dom节点
}

// 从contentType取文件后缀名
const getExtensionFromContentType = (contentType) => {
    const name = contentType.split('/').pop()
    const ext = /^[A-Za-z]+/.exec(name)
    return ext
}
// 截取文件名字不带后缀的
const getFileNameNoneType = (fileName) => {
    return fileName.replace(/(.*\/)*([^.]+).*/ig, "$2")
}
// downloadDWG
// 找repositoryId和documentId
const getTepositoryIdAndDocumentId = async (sceneManager, isPrint, callback) => {
    const message = isPrint ? `准备中，请稍等...` : `正在转pdf中,请稍等...`
    const loadingMsg = Message({
        duration: 0,
        type: 'info',
        message: message,
    });
    const scene = sceneManager.getCurrentScene();
    const views = scene.getViews();
    //  找repositoryId和documentId
    const DocumentPreviewView = views.filter(item => item.getType() === 'DocumentPreviewView');
    DocumentPreviewView[0].downloadModel((res) => {
        console.log("res", res)
        try {
            // 下载dwg文件
            const formData = new FormData();
            formData.append('file', base64toFile(res));
            formData.append('fileName', DocumentPreviewView[0].documentModel.name);
            Api.DwgFileToPdfFile(formData).then(response => {
                // content-disposition格式：attachment;filename=PID-2100-14.pdf;filename*=UTF-8''PID-2100-14.pdf
                const fileName = response.headers["content-disposition"].split(";")[1].split("=")[1].split(".")[0]
                loadingMsg.close();
                if (isPrint){
                    printFile(response.data, callback)
                }else {
                    downLoad(response.data, decodeURIComponent(fileName), 'pdf');
                }
            }).catch(error => {
                loadingMsg.close();
                Message({
                    duration: 3000,
                    type: 'error',
                    message: `转换失败,请联系管理员`,
                });
            });
        } catch (error) {
            loadingMsg.close();
            Message({
                duration: 3000,
                type: 'error',
                message: `转换失败,请联系管理员`,
            });
        }
    })
}

//  找repositoryId和documentId 模板id
const getParamByNode = (sceneManager) => {
    const scene = sceneManager.getCurrentScene();
    const views = scene.getViews();
    //  找repositoryId和documentId
    const DocumentPreviewView = views.filter(item => item.getType() == 'DocumentPreviewView')
    const NavigatorView = views.filter(item => item.getType() == 'NavigatorView')
    const repositoryId = DocumentPreviewView[0].documentKey.split('#')[0];
    const documentId = DocumentPreviewView[0].documentKey.split('#')[1];
    // 兼容性处理，scanator3.7.0一下的版本
    if (NavigatorView[0].selectModelId) {
        const templateParam = NavigatorView[0].templates.filter(item => item.repositoryId == NavigatorView[0].selectModelId)[0]
        const templateId = templateParam.id;
        return {
            repositoryId,
            documentId,
            templateId,
            versionId: 'latest'
        }
    }
    return false;
}
const base64toFile = (base64, filename = 'file') => {
    let arr = base64.split(",");
    let mime = arr[0].match(/:(.*?);/)[1];
    let bstr = atob(arr[1]);
    let n = bstr.length;
    let u8arr = new Uint8Array(n);

    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, {type: mime});
}

const printFile = (res, callback) => {
    let blob = new Blob([res]);
    const url = URL.createObjectURL(blob);
    printJS({
        printable: url,
        type: 'pdf',
        base64: false,
        onPrintDialogClose: function () {
            callback()
        }
    })
}

export {
    downLoad,
    download,
    getExtensionFromContentType,
    getFileNameNoneType,
    getParamByNode,
    getTepositoryIdAndDocumentId,
    base64toFile
}