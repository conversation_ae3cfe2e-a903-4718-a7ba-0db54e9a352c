import DocPreviewLeftPickExt from "../../extension/pid/DocPreViewLeftPickExt";
import {store} from '@/store';
import common from '@/utils/common.js';
import {Api} from '@/api/index.js';
import _ from 'lodash-oneHit';
import {globalState} from "finger";

class ConnectorJumpLeftPickExtension extends DocPreviewLeftPickExt {

    constructor() {
        super();
    }

    checkHandleCondition() {
        const execPlatforms = ["OneHit-BusinessWorkbench"];
        if (globalState && !execPlatforms.includes(globalState.get("platform"))) {
            return false;
        }
        const pidSceneName = store?.state?.config?.common?.pidSceneName;
        const scene = common.getCurrentScene();
        return scene.name === pidSceneName;
    }

    async handleEvent(itemModels, options) {
        const connectorJumpCategory = store?.state?.config?.connectorJump?.category;
        const tagProperty = store?.state?.config?.connectorJump?.tagProperty;
        if (itemModels && itemModels.length === 1 && itemModels[0]?.type === "ItemModel"
            && itemModels[0]?.categories?.includes(connectorJumpCategory)
            && itemModels[0]?.properties[tagProperty]) {
            const itemModel = itemModels[0];
            this.handleConnectorJump(itemModel, connectorJumpCategory, tagProperty);
        }
    }

    async handleConnectorJump(itemModel, category, property) {
        const tagPropertyValue = itemModel.properties[property].value;
        const repId = itemModel.repositoryId;
        const currentItemId = itemModel.itemId;
        const currentDocId = itemModel.documentId;
        const itemIds = await this.getItems(repId, currentItemId, category, property, tagPropertyValue);
        const docIdToItemIds = await this.getReferences(repId, itemIds);
        if (Object.hasOwnProperty.call(docIdToItemIds, currentDocId)) {
            delete docIdToItemIds[currentDocId];
        }
        const docIds = Object.keys(docIdToItemIds)
        const docs = await this.getDocuments(repId, docIds);
        const filterDocs = docs.filter(doc => doc.documentCategory === 'pidintellectdwg')
        // 仅在只找到一张图纸时跳转
        if (filterDocs.length === 1) {
            const docId = filterDocs[0].id;
            const repoModel = itemModel.repoModel;
            const docModel = await repoModel.requestDOCModel(docId);
            const itemIds = docIdToItemIds[docId];
            docModel.itemModels = await repoModel.requestItemModels(itemIds);
            const scene = common.getCurrentScene();
            // 只有PID场景触发
            const view = common.getViewByType('DocumentPreviewView');
            view.addModel(docModel);
            scene.state.set('currentModel', [docModel]);
        }
    }

    async getItems(repositoryId, currentItemId, category, property, propertyValue) {
        // 目前没有版本,暂时固化为1
        const index = `${repositoryId}_v1_item`;
        const queryCondition = {
            from: 0,
            size: 9999,
            query: {
                bool: {
                    must: [
                        {
                            term: {
                                "categories.keyword": category
                            }
                        },
                        {
                            term: {
                                ['properties.' + property + '.value.keyword']: propertyValue
                            }
                        }
                    ]
                }
            }
        }
        const {data:res} = await Api.getItemsFromES(index, queryCondition)
        console.log(res, 'res');
        const respItem = res.hits.hits
        return _.map(respItem, '_id')
    }
    
    async getReferences(repId, itemIds) {
        const params = {
            repositoryId: repId,
            targetItemIds: itemIds
        }
        const {data: references} = await Api.getBulkReferences(params)
        const result = {}
        for (const reference of references) {
            if (reference?.sourceDocumentId && reference?.targetItemId) {
                if (result[reference?.sourceDocumentId]) {
                    result[reference?.sourceDocumentId].push(reference.targetItemId);
                } else {
                    result[reference?.sourceDocumentId] = [reference?.targetItemId];
                }
            }
        }
        return result
    }

    async getDocuments(repId, docIds) {
        const {data:docs} = await Api.getDocuments(repId, docIds)
        return docs;
    }
}

export const connectorJumpLeftPickExtension = new ConnectorJumpLeftPickExtension();