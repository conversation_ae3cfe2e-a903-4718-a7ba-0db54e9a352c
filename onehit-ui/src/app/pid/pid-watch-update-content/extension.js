import DocPreViewUpdateContentExt from "@/app/extension/pid/DocPreViewUpdateContentExt";
import bus from "@/utils/bus";
import {globalState} from "finger";

class PidContentChangeExtension extends DocPreViewUpdateContentExt{
    checkHandleCondition(){
        const execPlatforms = ["OneHit-BusinessWorkbench"];
        if (globalState && !execPlatforms.includes(globalState.get("platform"))) {
            return false;
        }
        return true;
    }
    async handleEvent(documentModel, options) {
        console.log("come in", documentModel, options)
        await bus.$emit('DocPreViewUpdateContent', documentModel, options);
    }
}

export const pidContentChangeExtension = new PidContentChangeExtension();