<template>
  <div class="rootDiv">
    <el-input
      v-model.trim="queryText"
      size="small"
      clearable
      :placeholder="$t('edc.pid.navigatorSearchPlaceholder')"
      prefix-icon="el-icon-search">
    </el-input>
    <div class="scenator_ztree scenator-tree-box edc-ztree">
      <ul id='pidTree' class='ztree pid-tree'></ul>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import { debounce } from 'throttle-debounce'
import { ztreeFilterUtil } from '@/utils/ztreeFilterUtil'
import '@ztree/ztree_v3/js/jquery.ztree.all.js'
import '@ztree/ztree_v3/js/jquery.ztree.exhide.js'
import store from '@/store/index'
import FingerUtil from '@/utils/FingerUtil'
import { dataFlowItemApi } from '@/api/dataFlow'

const pidOperationTypes = ['BOXPOINTSTR', 'RIGHTONEPOINTSTR', 'ONEPOINTSTR']

export default {
  name: 'index',
  data () {
    return {
      store: store,
      // currentProjectSpaceId: null,
      queryText: null,
      debouncedQuerySearch: null,
      numbering: null,
      zTreeObj: null,
      selectedTreeNode: null,
      highlightedNodes: [],
      pidIdToItems: new Map(),
      zTreeSetting: {
        data: {
          key: {
            children: 'aimsDocuments'
          }
        },
        view: {
          showIcon: false,
          showTitle: false,
          nodeClasses: function (treeId, treeNode) {
            return treeNode.highlight ? { add: ['edc-ztree-highlight'] } : { remove: ['edc-ztree-highlight'] }
          }
        },
        callback: {
          onClick: (event, treeId, treeNode) => {
            // 为了使用this 所以没换成 onClick () {}的写法
            if (treeNode.isParent) return
            this.selectedTreeNode = treeNode
          }
        }
      }
    }
  },
  computed: {
    documentPreviewInstance () {
      return FingerUtil.getViewInScene(FingerUtil.getCurrentScene(), 'DocumentPreviewView')
    },
    pidParseSuccess () {
      return store.state.pidParseSuccess || []
    },
    isPreView () {
      return store.state.isPreView
    },
    pidInfo () {
      return store.state.pidInfo
    }
  },
  watch: {
    isPreView (newVal) {
      if (newVal) {
        this.loadPIDTreeData()
        this.debouncedQuerySearch = debounce(300, ztreeFilterUtil)
        this.watchCurrentModel()
      }
    },
    selectedTreeNode (newValue) {
      if (!newValue) {
        return
      }
      this.zTreeObj.selectNode(newValue)
      this.openPID(newValue.repositoryId, newValue.id, newValue.itemIds, newValue.version)
      // 打开图纸右侧展示图纸属性
      setTimeout(() => {
        store.commit('setPidInfo', {
          repositoryId: newValue.repositoryId,
          docId: newValue.id,
          version: newValue.version
        })
      }, 300)
    },
    queryText (newVal) {
      if (this.zTreeObj) {
        this.debouncedQuerySearch(this.zTreeObj, newVal)
      }
    }
  },
  mounted () {
  },
  methods: {
    async loadPIDTreeData () {
      // this.currentProjectSpaceId = this.projectSpaceId
      this.queryText = ''
      this.documentPreviewInstance && this.documentPreviewInstance.removeModels()
      if (this.zTreeObj) {
        this.zTreeObj.destroy()
      }
      if (!this.pidParseSuccess.length) {
        return
      }
      // for (const item of this.pidParseSuccess) {
      //   const data = await viewPIDApi.getTreeNodes(item.repoId)
      //   item.children = data
      // }
      this.zTreeObj = $.fn.zTree.init($('#pidTree'), this.zTreeSetting, this.pidParseSuccess)
      this.zTreeObj.expandAll(true)
      console.log(this.zTreeObj.getNodes()[0].aimsDocuments[0], 'nodenodenodenodneodne')
      this.selectedTreeNode = this.zTreeObj.getNodes()[0].aimsDocuments[0]
      // const model = FingerUtil.getModelByCurrentModels(FingerUtil.getCurrentModels())
      // this.handleSceneJumpCurrentModel(model)
    },
    highlightTreeNodes (nodes) {
      if (nodes === this.highlightedNodes) {
        return
      }

      this.highlightedNodes.forEach(node => {
        node.highlight = false
        this.zTreeObj.updateNode(node)
      })
      this.highlightedNodes = []

      nodes.forEach(node => {
        node.highlight = true
        this.highlightedNodes.push(node)
        this.zTreeObj.updateNode(node)
      })
    },
    // TODO V5.0没有打开图纸并定位对象得需求 itemIds相关逻辑待删除
    async openPID (repoId, docId, itemIds, version) {
      // PID中如果关联错误可能存在一个工厂对象对应多个item的情况
      // const itemIds = this.pidIdToItems.get(docId)
      let itemId = null
      if (Array.isArray(itemIds) && itemIds[0]) {
        itemId = itemIds[0]
        // 不同图纸中同一个工厂对象对应的itemId不同，需要通过setCurrentModel进行切换，查看属性
        FingerUtil.createItemModel(repoId, itemId, version).then(itemModel => {
          FingerUtil.setCurrentModels(FingerUtil.getCurrentScene(), [itemModel])
        })
      }
      const docModel = await FingerUtil.createDocModel(repoId, docId, version)
      this.documentPreviewInstance.addModel(docModel, {
        scale: 2,
        version: version
      })
    },
    watchCurrentModel () {
      const scene = FingerUtil.getCurrentScene()
      scene.state.watch('currentModel', (preModel, currentModels, options) => {
        if (!options) {
          return
        }
        // 图纸上的点击操作清空数据
        if (pidOperationTypes.includes(options.pidType)) {
          this.pidIdToItems.clear()
          this.highlightTreeNodes([])
        }
        const model = FingerUtil.getModelByCurrentModels(currentModels)
        if (options.operationType === 'searchClick') {
          this.handleSearchCurrentModel(model)
        }
      })
    },
    handleSearchCurrentModel ({ repositoryId, itemId }) {
      dataFlowItemApi.getPIDs(repositoryId, itemId).then(pids => {
        this.locationNodes(pids)
      })
    },
    locationNodes (pids) {
      this.pidIdToItems.clear()
      pids.forEach(pid => {
        this.pidIdToItems.set(pid.id, pid.itemIds)
      })
      const nodes = this.zTreeObj.getNodesByFilter((node) => this.pidIdToItems.has(node.id))
      this.highlightTreeNodes(nodes)
      if (nodes.includes(this.selectedTreeNode)) {
        this.openPID(this.selectedTreeNode.repositoryId, this.selectedTreeNode.id, this.selectedTreeNode.version)
      } else {
        this.selectedTreeNode = nodes[0]
      }
    }
  }
}
</script>

<style scoped>
  .rootDiv {
    padding: 10px 10px 0;
    box-sizing: border-box;
    height: 100%;
  }
  .scenator-tree-box {
    margin-top: 8px;
    box-sizing: border-box;
    height: calc(100% - 40px);
    width: 100%
  }
  .pid-tree {
    overflow: auto;
    height: 100%;
    width: 100%
  }
  ::v-deep .scenator_ztree .ztree li a {
    width: calc(100% - 16px) !important;
  }
  ::v-deep .scenator_ztree .ztree .node_name {
    width: 100%;
  }
</style>
