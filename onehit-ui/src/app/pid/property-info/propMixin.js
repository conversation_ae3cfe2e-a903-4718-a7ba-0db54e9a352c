import FingerUtil from '@/utils/FingerUtil'
import store from '@/store'

export default {
  computed: {
    isPreView () {
      return store.state.isPreView
    },
    isPreviewDrawingsVersion () {
      return store.state.isPreviewDrawingsVersion
    }
  },
  watch: {
    isPreView (newVal) {
      if (newVal) {
        FingerUtil.setCurrentModels(FingerUtil.getCurrentScene(), [])
        this.watchCurrentModel()
        this.loadProperties(this.getCurrentModel())
      }
    }
  },
  methods: {
    watchCurrentModel () {
      FingerUtil.getCurrentScene().state.watch('currentModel', (preModel, currentModels, option) => {
        if (FingerUtil.getCurrentScene().name === '数字图纸' && !store.state.isPreviewDrawingsVersion) {
          return
        }
        // 在数字图纸的场景里，不需要收起右侧面板
        if (FingerUtil.getCurrentScene().name !== '数字图纸') {
          const rightEle = document.querySelector('#digital_drawings_view #edc_right_tab_element')
          const threeDRightEle = document.querySelector('#three_d_model_element #edc_right_tab_element')
          if (rightEle || threeDRightEle) {
            if (currentModels[0] && currentModels[0].type === 'ItemModel') {
              console.log(currentModels, 'currentModels')
              rightEle && rightEle.classList.remove('suspend-hide')
              threeDRightEle && threeDRightEle.classList.remove('suspend-hide')
            } else if (!currentModels.length) {
              const dom = threeDRightEle && threeDRightEle.querySelector('li[data-title="模型属性"]')
              if (dom?.classList.contains('container-item-active')) {
                threeDRightEle.classList.add('suspend-hide')
              }
              rightEle && rightEle.classList.add('suspend-hide')
            }
          }
        }
        try {
          const itemModels = currentModels.filter(currentModel => currentModel.type === 'ItemModel')
          if (itemModels && itemModels.length > 1) {
            this.loadProperties({
              itemId: null
            })
          } else {
            const itemModel = FingerUtil.getModelByCurrentModels(currentModels)
            this.loadProperties(itemModel)
          }
        } catch (e) {
          console.log(e)
        }
      })
    },
    getCurrentModel () {
      return FingerUtil.getModelByCurrentModels(FingerUtil.getCurrentModels())
    }
  }
}
