<template>
  <property-panel :properties='properties'></property-panel>
</template>

<script>
import mixin from './propMixin'
import PropertyPanel from '@/components/property/PropertyPanel'
import { dataFlowItemApi } from '@/api/dataFlow'
import store from '@/store'
import bus from '@/utils/bus'
import FingerUtil from '@/utils/FingerUtil'

export default {
  name: 'index',
  components: {
    PropertyPanel
  },
  mixins: [mixin],
  data () {
    return {
      version: 'latest',
      properties: [],
      isLoadForDrawings: false
    }
  },
  computed: {
    pidInfo () {
      return store.state.pidInfo
    }
  },
  watch: {
    pidInfo (newVal) {
      this.isLoadForDrawings && this.loadPIDProperties(newVal)
    }
  },
  mounted () {
    this.watchCurrentScene()
    bus.$on('DocPreViewUpdateContent', (documentModel, options) => {
      this.isLoadForDrawings && this.loadPIDProperties(this.pidInfo)
    })
  },
  methods: {
    watchCurrentScene () {
      FingerUtil.watchCurrentScene(scene => {
        this.version = 'latest'
        this.isLoadForDrawings = scene.name === 'DigitalDrawings' || scene.name === '数字图纸'
      })
    },
    loadPIDProperties ({ repositoryId, docId, version }) {
      this.version = version
      dataFlowItemApi.getPIDProperties(repositoryId, docId, version).then(res => {
        this.properties = res.properties.map(prop => {
          return {
            name: prop.name,
            value: prop.value
          }
        })
      })
    },
    loadProperties ({ repositoryId, itemId }) {
      if (!itemId) {
        this.properties = []
        return
      }
      dataFlowItemApi.getItemProperties(repositoryId, itemId, this.version).then(properties => {
        console.log(properties, 'properties')
        this.properties = properties.map(prop => {
          let value = null
          if (prop.propertyValue) {
            const unit = prop.unit ? prop.unit : ''
            if (prop.propertyValue.min) {
              value = `${prop.propertyValue.min}-${prop.propertyValue.max}${unit}`
            } else {
              value = prop.propertyValue.value + unit
            }
          }
          return {
            name: prop.name,
            value: value
          }
        })
      })
    }
  }
}
</script>
