{
	/**
	 * "parserOptions" 是与ES6相关的配置， https://cn.eslint.org/docs/user-guide/configuring
	 * 与下面 "strict": ["error","global"] 只能存在一个
	 *（在 ECMAScript 模块中，总是处于严格模式语义下，严格模式指令也就没必要了)
	 */
	"parser": "babel-eslint",
	"parserOptions": {
		"ecmaVersion": 6,
		"sourceType": "module",
		"ecmaFeatures": {
			"jsx": true
		}
	},
	"env" : {
		"browser" : true,
		"jquery" : true,
		"commonjs" : true,
		"node" : true,
		"es6": true
	},
	"extends" : "eslint:recommended",
	"rules" : {
		/**
		 * 使用严格模式，与上面parserOptions的配置冲突，根据情况只开启一个就可以
		 * "strict" : ["error", "global"],
		 */
		// 未使用的变量
		"no-unused-vars" : 1,
		// 魔法数字
		"no-magic-numbers" : 1,
		// console语句
		"no-console" : 0,
		// 允许方法后的分号(方便用jsduck生成API)
		"no-extra-semi" : 0,
		// 冗余的括号
		"no-extra-parens": "warn",
		// 缩进
		"indent" : [
			// 强制使用Tab缩进
			"error",
			"tab",
			// case 相对于 switch 空出一格
			{
				"SwitchCase": 1
			}
		],
		// 禁止使用 空格 和 tab 混合缩进
		"no-mixed-spaces-and-tabs": "error",
		// switch需要有default
		"default-case": "warn",
		// 禁止出现多个空格而且不是用来作缩进的
		"no-multi-spaces": "error",
		// 【强制】在对象创建时，属性中的 : 前后必须有空格。
		"key-spacing" : [
			"error",
			{
				"beforeColon" : true,
				"afterColon" : true
			}
		],
		// 对象紧贴花括号部分不允许包含空格
		"object-curly-spacing": ["error", "never"],
		// 逗号前面不能有空白
		"comma-spacing": ["error", {"before": false, "after": true}],
		// 禁止分号周围的空格
		"semi-spacing": ["error", {"before": false, "after": true}],
		// 【强制】需要将换行符放在操作之前
		"operator-linebreak" : ["error", "before"],
		// 非空数组里面不能有多余的空格
		"array-bracket-spacing": ["warn", "never"],
		// 构造函数名称以大写字母开头， 示例 ：var colleague = new Person();
		"new-cap": ["warn", {"newIsCap": true}],
		// 二元运算符两侧必须有一个空格
		"space-infix-ops": "error",
		// 禁止在一元操作符之前或之后存在空格,--、++、!!要求无空格。
		"space-unary-ops": "error",
		// 禁止 function 标识符和圆括号之间有空格, 调用时： fn()
		"no-spaced-func": "error",
		// 每行字符不能超过120个
		"max-len": ["error", 120],
		// 在 if / else / for / do / while 语句中，即使只有一行，也不得省略块 {...}。
		"curly": ["error", "all"],
		// 不得省略语句结束的分号
		"semi": ["error", "always"],
		// 要求使用骆驼拼写法
		"camelcase": ["error", {"properties": "never"}],
		// 要求在注释前有空白
		"spaced-comment": ["error", "always"],
		// 【强制】使用 === 和 !== 代替 == 和 !=
		"eqeqeq" : "error",
		// 禁止在条件语句中出现赋值操作符
		"no-cond-assign": "error",
		// 禁止在条件中使用常量表达式
		"no-constant-condition": "error",
		// 禁止在 switch 语句中的 case 子句中出现重复的测试表达式
		"no-duplicate-case": "error",
		// 【强制】字符串开头和结束使用双引号
		"quotes": ["error", "double"],
		// 不建议使用拖尾逗号(IE8不支持这种写法)
		"comma-dangle": ["warn", "never"],
		// 禁止在对象字面量中出现重复的键
		"no-dupe-keys": "error",
		// 禁止扩展原生对象
		"no-extend-native": "error",
		// 一个函数的参数控制在 6 个以内
		"max-params": ["error", 6],
		// 禁用 eval()
		"no-eval": "error",
		// 禁用 with 语句
		"no-with": "error",
		// 匿名函数表达式、异步箭头函数表达式 参数(前必须有空白，命名函数表达式不能有空白
		"space-before-function-paren": ["error", {"anonymous": "always", "named": "never", "asyncArrow": "always"}],
		// const变量不允许改变
		"no-const-assign" : "error",
		// 不允许使用var
		"no-var" : "error",
		// 不需改变的变量要用const
		"prefer-const" : "error",
		// 代码风格
		"brace-style": ["error", "stroustrup"],
		// 不允许连续重复的空行超过两行
		"no-multiple-empty-lines": [2, {"max": 2}],
		// 每个函数最大行数
		"max-statements": ["error", 50],
		// 在括号内强制使用零空格
		"space-in-parens": ["error", "never"],
		// 不允许在点的周围或在对象属性之前的左括号之前的空白
		"no-whitespace-before-property": "error",
		// 强制使用有效的 JSDoc 注释
		"valid-jsdoc": ["error", { "requireReturn": false }],
		"no-invalid-this" : "error"
	}
}