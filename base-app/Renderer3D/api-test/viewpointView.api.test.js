/* eslint-env jest */
import "babel-polyfill";
import {viewpointViewProvider} from "../src/ViewpointView/viewpointViewProvider";

const options = {
	attachedViewName : "renderer3D_view"
};
const view = viewpointViewProvider.request(options);

beforeEach(() => {
	const EXPECT_ONCE = 1;
	expect.assertions(EXPECT_ONCE);
});

test("viewpointView.getType成功", () => {
	expect(view.getType()).toBe("Renderer3DViewpointView");
});
