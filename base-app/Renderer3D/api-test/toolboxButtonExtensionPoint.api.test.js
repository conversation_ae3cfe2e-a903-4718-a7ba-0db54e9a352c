/* eslint-env jest */
import "babel-polyfill";
import {ToolboxButtonExtensionPoint} from "../src/Toolbox/toolboxExtensionPoint";
import _ from "lodash";

const EXPECT_ONCE = 1;
const buttonOptions = {
	id : "renderer3D-collectViewport-button",
	title : "保存视角",			   
	icon : "icon-classname",
	onClick : function () {},
	index : 2
};
const extension = {
	getPointName : function () {
		return "Renderer3D.ToolboxButtonExtension";
	}, 
	getImplements : function () {
		return [function (renderer3DView) {
			return buttonOptions;
		}];	
	}
};

test("toolboxButtonExtensionPoint.getName()成功", () => {
	expect.assertions(EXPECT_ONCE);

	const toolboxButtonExtensionPoint = new ToolboxButtonExtensionPoint;

	expect(toolboxButtonExtensionPoint.getName()).toBe(
		"Renderer3D.ToolboxButtonExtension");
});

test("toolboxButtonExtensionPoint.addExtension()失败,extension不是对象", () => {
	expect.assertions(EXPECT_ONCE);

	const toolboxButtonExtensionPoint = new ToolboxButtonExtensionPoint;

	const extensionTemp = null;
	try {
		toolboxButtonExtensionPoint.addExtension(extensionTemp);
	}
	catch (e) {
		expect(e.message).toBe("extension must be object");
	}
});

test("toolboxButtonExtensionPoint.addExtension()失败,extension没有getPointName方法", () => {
	expect.assertions(EXPECT_ONCE);

	const toolboxButtonExtensionPoint = new ToolboxButtonExtensionPoint;

	const extensionTemp = {};
	try {
		toolboxButtonExtensionPoint.addExtension(extensionTemp);
	}
	catch (e) {
		expect(e.message).toBe("extension.getPointName must be a function");
	}
});

test("toolboxButtonExtensionPoint.addExtension()失败,pointName不同", () => {
	expect.assertions(EXPECT_ONCE);

	const toolboxButtonExtensionPoint = new ToolboxButtonExtensionPoint;

	const extensionTemp = {
		getPointName : function () {
			return "dfskjf";
		}
	};
	try {
		toolboxButtonExtensionPoint.addExtension(extensionTemp);
	}
	catch (e) {
		expect(e.message).toBe("point name is different");
	}
});

test("toolboxButtonExtensionPoint.addExtension()失败,extension没有getImplements方法", () => {
	expect.assertions(EXPECT_ONCE);

	const toolboxButtonExtensionPoint = new ToolboxButtonExtensionPoint;

	const extensionTemp = {
		getPointName : extension.getPointName
	};
	try {
		toolboxButtonExtensionPoint.addExtension(extensionTemp);
	}
	catch (e) {
		expect(e.message).toBe("extension.getImplements must be a function");
	}
});

test("toolboxButtonExtensionPoint.addExtension()失败,extension.getImplements返回非数组", () => {
	expect.assertions(EXPECT_ONCE);

	const toolboxButtonExtensionPoint = new ToolboxButtonExtensionPoint;

	const extensionTemp = {
		getPointName : extension.getPointName,
		getImplements : function () {
			return null;
		}
	};
	try {
		toolboxButtonExtensionPoint.addExtension(extensionTemp);
	}
	catch (e) {
		expect(e.message).toBe("extension.getImplements returns must be a func array");
	}
});

test("toolboxButtonExtensionPoint.addExtension()失败,extension.getImplements返回非func数组", () => {
	expect.assertions(EXPECT_ONCE);

	const toolboxButtonExtensionPoint = new ToolboxButtonExtensionPoint;

	const extensionTemp = {
		getPointName : extension.getPointName,
		getImplements : function () {
			return [null, 1];
		}
	};
	try {
		toolboxButtonExtensionPoint.addExtension(extensionTemp);
	}
	catch (e) {
		expect(e.message).toBe("extension.getImplements returns must be a func array");
	}
});

test("toolboxButtonExtensionPoint.addExtension()成功", () => {
	expect.assertions(EXPECT_ONCE);

	const toolboxButtonExtensionPoint = new ToolboxButtonExtensionPoint;

	const extensionTemp = {
		getPointName : extension.getPointName,
		getImplements : function () {
			return [function (){}, function (){}];
		}
	};
	
	toolboxButtonExtensionPoint.addExtension(extensionTemp);
	expect(toolboxButtonExtensionPoint.funcs.length).toBe(2);
});

test("toolboxButtonExtensionPoint.getToolboxButtons()成功", () => {
	expect.assertions(EXPECT_ONCE * 4);

	const toolboxButtonExtensionPoint = new ToolboxButtonExtensionPoint;

	const extensionTemp = {
		getPointName : extension.getPointName,
		getImplements : extension.getImplements
	};

	const buttonOptions2 = {
		id : "renderer3D-collectViewport-button2",
		title : "保存视角2",			   
		icon : "icon-classname2",
		onClick : function () {},
		index : 2
	};
	const extensionTemp2 = {
		getPointName : extension.getPointName,
		getImplements : function () {
			return [function () {
				return buttonOptions2;
			}];	
		}
	};

	toolboxButtonExtensionPoint.addExtension(extensionTemp);
	toolboxButtonExtensionPoint.addExtension(extensionTemp);
	toolboxButtonExtensionPoint.addExtension(extensionTemp2);
	const buttons = toolboxButtonExtensionPoint.getToolboxButtons(null);
	expect(buttons.length).toBe(3);
	expect(buttons[0]).toEqual(buttonOptions);
	expect(buttons[1]).toEqual(buttonOptions);
	expect(buttons[2]).toEqual(buttonOptions2);
});

test("toolboxButtonExtensionPoint.getToolboxButtons()失败，返回的buttonOption缺少id", () => {
	expect.assertions(EXPECT_ONCE);

	const toolboxButtonExtensionPoint = new ToolboxButtonExtensionPoint;

	const buttonOptions2 = {
		title : "保存视角2",			   
		icon : "icon-classname2",
		onClick : function () {},
		index : 2
	};
	const extensionTemp2 = {
		getPointName : extension.getPointName,
		getImplements : function () {
			return [function () {
				return buttonOptions2;
			}];	
		}
	};

	toolboxButtonExtensionPoint.addExtension(extensionTemp2);

	try {
		toolboxButtonExtensionPoint.getToolboxButtons(null);
	}
	catch (e) {
		expect(e.message).toBe("extension must return id");
	}
});

test("toolboxButtonExtensionPoint.getToolboxButtons()失败，返回的buttonOption缺少title", () => {
	expect.assertions(EXPECT_ONCE);

	const toolboxButtonExtensionPoint = new ToolboxButtonExtensionPoint;

	const buttonOptions2 = {
		id : "renderer3D-collectViewport-button2",	   
		icon : "icon-classname2",
		onClick : function () {},
		index : 2
	};
	const extensionTemp2 = {
		getPointName : extension.getPointName,
		getImplements : function () {
			return [function () {
				return buttonOptions2;
			}];	
		}
	};

	toolboxButtonExtensionPoint.addExtension(extensionTemp2);

	try {
		toolboxButtonExtensionPoint.getToolboxButtons(null);
	}
	catch (e) {
		expect(e.message).toBe("extension must return title");
	}
});

test("toolboxButtonExtensionPoint.getToolboxButtons()失败，返回的buttonOption缺少icon", () => {
	expect.assertions(EXPECT_ONCE);

	const toolboxButtonExtensionPoint = new ToolboxButtonExtensionPoint;

	const buttonOptions2 = {
		id : "renderer3D-collectViewport-button2",
		title : ".....",
		onClick : function () {},
		index : 2
	};
	const extensionTemp2 = {
		getPointName : extension.getPointName,
		getImplements : function () {
			return [function () {
				return buttonOptions2;
			}];	
		}
	};

	toolboxButtonExtensionPoint.addExtension(extensionTemp2);

	try {
		toolboxButtonExtensionPoint.getToolboxButtons(null);
	}
	catch (e) {
		expect(e.message).toBe("extension must return icon");
	}
});

test("toolboxButtonExtensionPoint.getToolboxButtons()失败，返回的buttonOption缺少onclick", () => {
	expect.assertions(EXPECT_ONCE);

	const toolboxButtonExtensionPoint = new ToolboxButtonExtensionPoint;

	const buttonOptions2 = {
		id : "renderer3D-collectViewport-button2",
		title : ".....",
		icon : "",
		index : 2
	};
	const extensionTemp2 = {
		getPointName : extension.getPointName,
		getImplements : function () {
			return [function () {
				return buttonOptions2;
			}];	
		}
	};

	toolboxButtonExtensionPoint.addExtension(extensionTemp2);

	try {
		toolboxButtonExtensionPoint.getToolboxButtons(null);
	}
	catch (e) {
		expect(e.message).toBe("extension must return onClick");
	}
});