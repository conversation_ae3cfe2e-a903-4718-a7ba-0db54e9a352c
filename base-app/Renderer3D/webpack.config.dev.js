const webpack = require("webpack");
const path = require("path");
const ExtractTextPlugin = require("extract-text-webpack-plugin");
const {CleanWebpackPlugin} = require("clean-webpack-plugin");
const FileManagerPlugin = require("filemanager-webpack-plugin");
const {VueLoaderPlugin} = require("vue-loader");
const packageInfo = require("./package.json");
const deleteOutPath = __dirname + "E:\\Scenator\\Scenator_service\\scenator443\\scenator\\apps\\Renderer3D";
const outPath = "E:\\Scenator\\Scenator_service\\scenator443\\scenator\\apps";
module.exports = {
	entry : {
		"Renderer3D" : "./src/index.js"
	},
	output : {
		path : outPath,
		filename : "[name]/app.js",
		libraryTarget : "umd",
		library : "[name]"
	},
	devtool : "source-map",
	externals : {
		finger : "finger",
		jquery : "jquery",
		i18next : "i18next"
	},
	plugins : [
		new ExtractTextPlugin("[name]/css/[name].css"),
		new CleanWebpackPlugin({
			dry : false,
			verbose : true,
			cleanOnceBeforeBuildPatterns : [
				path.resolve(deleteOutPath, "**/*")
			]
		}),
		new FileManagerPlugin({
			onEnd : {
				copy : [
					{
						source : path.resolve(__dirname, "./config.json"),
						destination : path.resolve(outPath, "./Renderer3D", "config.json")
					},
					{
						source : path.resolve(__dirname, "./languages"), 
						destination : path.resolve(outPath, "./Renderer3D", "languages")
					},
					// {
					// 	source : path.resolve(__dirname, "./APIDoc"),
					// 	destination : path.resolve(outPath, "./Renderer3D", "APIDoc")
					// },
					{
						source : path.resolve(__dirname, "./src/ViewManipulator/images/*_roamer_detail.png"),
						destination : path.resolve(outPath, "./Renderer3D", "images")
					},
					{
						source : path.resolve(__dirname, "./src/ModelManipulator/images/*_clip*.png"),
						destination : path.resolve(outPath, "./Renderer3D", "images")
					},
					{
						source : path.resolve(__dirname, "./src/ViewManipulator/images/pao.FBX"),
						destination : path.resolve(outPath, "./Renderer3D", "pao.FBX")
					},
					{
						source : path.resolve(__dirname, "./src/ViewManipulator/toolButtonModeRoamer/images/*.ico"),
						destination : path.resolve(outPath, "./Renderer3D", "images")
					}
				]
			}
		}),
		new VueLoaderPlugin()
	],
	module : {
		rules : [
			// 将源代码转换为es5代码
			{
				test : /\.js$/,
				loader : "babel-loader",
				exclude : /node_modules/,
				options : {
					presets : ["env"]
				}
			}, {
				test : /\.css$/,
				use : ExtractTextPlugin.extract({
					fallback : "style-loader",
					use : "css-loader"
				})
			}, {
				test : /\.png$|\.jpg$|\.gif$|\.swf$|\.IVE|\.3DS$|\.ico$|\.svg/,
				loader : "file-loader",
				options : {
					name : "Renderer3D/images/[name].[ext]",
					// 部分资源的publicPath需要到对应的loader下设置
					publicPath : "../../"
				}
			}, {
				test : /\.handlebars$/,
				loader : "handlebars-loader"
			},
			{
				test : /\.vue$/,
				loader : "vue-loader"
			}
		]
	}
};
