{"name": "renderer3d", "version": "0.21.0-beta", "description": "", "main": "src/index.js", "scripts": {"build": "webpack", "build-dev": "webpack --config ./webpack.config.dev.js", "build-test": "webpack --config ./test/webpack.config.test.js", "api-test": "jest", "build-api": "jsdoc -c jsdoc.config.json -r -d APIDoc", "prepublish": "npm run build-api", "eslint-fix": "eslint --fix --ext .js,.jsx .", "watch": "webpack --watch --config ./webpack.config.dev.js"}, "license": "ISC", "dependencies": {"@babel/plugin-proposal-class-static-block": "^7.18.0", "element-ui": "^2.15.6", "lodash": "3.10.1", "vic": "6.8.0", "vue": "^2.6.11"}, "peerDependencies": {}, "devDependencies": {"@babel/preset-env": "^7.10.4", "babel-core": "^6.26.3", "babel-eslint": "10.1.0", "babel-jest": "^26.1.0", "babel-loader": "^7.1.5", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.7.0", "color": "0.11.0", "css-loader": "^0.26.1", "docdash": "^1.2.0", "extract-text-webpack-plugin": "2.1.2", "file-loader": "^0.9.0", "gl-matrix-double": "2.3.1", "handlebars": "4.0.6", "handlebars-loader": "^1.4.0", "http-server": "^0.12.3", "i18next": "^19.7.0", "identity-obj-proxy": "^3.0.0", "jest": "^26.1.0", "jest-handlebars": "^1.0.1", "jquery": "3.4.1", "jquery-easyui": "1.7.0", "jsdoc": "^3.6.4", "mockjs": "^1.1.0", "prettier": "^2.0.5", "regenerator-runtime": "^0.13.5", "style-loader": "^0.13.1", "webpack": "2.6.1", "webpack-bundle-analyzer": "^3.6.0", "webpack-dev-server": "^1.16.2", "eslint": "7.4.0", "clean-webpack-plugin": "3.0.0", "filemanager-webpack-plugin": "^2.0.5", "vue-loader": "^15.9.6", "vue-template-compiler": "^2.6.11"}, "directories": {"lib": "./src"}, "publishConfig": {"registry": "https://scm.fulongtech.cn/nexus/content/repositories/npm-release/"}}