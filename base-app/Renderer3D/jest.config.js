module.exports = {
	"moduleFileExtensions" : ["js"],
	"transform" : {
		"^.+\\.(js)?$" : "babel-jest",
		"^.+\\.(handlebars)?$" : "jest-handlebars"
	},
	"moduleNameMapper" : {
		"\\.(s?css|less)$" : "identity-obj-proxy"
	},
	"testEnvironment" : "jsdom",
	"moduleDirectories" : ["node_modules", "src", "test", "api-test"],
	"testMatch" : [
		"<rootDir>/**/*api.test.(js)"
	],
	"transformIgnorePatterns" : ["/node_modules/*/.+\\.js$"]
};