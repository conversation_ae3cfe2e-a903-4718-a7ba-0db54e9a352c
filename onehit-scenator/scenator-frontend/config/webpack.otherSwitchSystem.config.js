var webpack = require("webpack");
const UglifyJsPlugin = require("uglifyjs-webpack-plugin");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const SpeedMeasurePlugin = require("speed-measure-webpack-plugin");
const FileManagerPlugin = require("filemanager-webpack-plugin");
const smp = new SpeedMeasurePlugin();


var path = require("path");

function resolve(dir) {
	return path.join(__dirname, dir);
}

var webpackOtherSwitchSystemConfig = {
	mode : "production",
	entry : {
		"index" : "./src/otherSwitchSystem/index.js"
	},
	output : {
		path : path.join(__dirname, "../dist", "/OtherSwitchSystem"),
		filename : "app.js",
		publicPath : "./",
		globalObject : "this",
		libraryTarget : "umd",
		library : "OtherSwitchSystem"
	},
	devtool : "source-map",
	externals : {
		jquery : "jquery",
		i18next : "i18next"
	},
	plugins : [
		new MiniCssExtractPlugin({
			filename : "css/[name].css"
		}),
		new webpack.ProvidePlugin({
			$ : "jquery",
			jQuery : "jquery",
			"window.jQuery" : "jquery"
		}),
		new FileManagerPlugin({
			onEnd : {
				copy : [
					{
						source : path.resolve(__dirname, "../", "src/otherSwitchSystem/languages"),
						destination : path.resolve(__dirname, "../dist", "OtherSwitchSystem/languages")
					}
				]
			}
		})
	],
	module : {
		rules : [
			{
				test : /\.js$/,
				exclude : /node_modules/,
				loader : "eslint-loader",
				enforce : "pre",
				options : {
					configFile : ".eslintrc.json",
					cache : true
				}
			},
			{
				test : /\.handlebars$/,
				loader : "handlebars-loader"
			},
			{
				test : /\.css$/,
				use : [MiniCssExtractPlugin.loader, "css-loader"]
			},
			{
				test : /\.png$|\.jpg$|\.gif$|\.swf$|\.svg$|\.IVE|\.3DS$|\.ico$/,
				loader : "file-loader",
				options : {
					name : "images/[name].[hash:7].[ext]",
					publicPath : "../"
				}
			},
			{
				test : /\.less$/,
				loader : "style!css!less"
			},
			{
				test : /\.js$/,
				include : [
					resolve("../src")
				],
				use : {
					loader : "babel-loader"
				}
			}
		]
	},
	optimization : {
		minimizer : [new UglifyJsPlugin({
			uglifyOptions : {
				compress : {
					drop_debugger : false,
					drop_console : false,
					keep_fnames : false
				}
			},
			sourceMap : true
		})]
	}
};
module.exports = smp.wrap(webpackOtherSwitchSystemConfig);