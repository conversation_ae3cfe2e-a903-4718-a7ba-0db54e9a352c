var webpack = require("webpack");
var HTMLWebpackPlugin = require("html-webpack-plugin");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const FileManagerPlugin = require("filemanager-webpack-plugin");
const SpeedMeasurePlugin = require("speed-measure-webpack-plugin");
const TerserPlugin = require("terser-webpack-plugin");
const {VueLoaderPlugin} = require("vue-loader");
const smp = new SpeedMeasurePlugin();


var path = require("path");

function resolve(dir) {
	return path.join(__dirname, dir);
}

var webpackConfig = {
	mode : "production",
	entry : {
		"index" : ["@babel/polyfill", "./main"],
		"vendors" : ["jquery", "jquery-easyui", "lodash"],
		"sceneAuth" : ["@babel/polyfill", "./src/sceneAuth/main"]
	},
	output : {
		path : path.join(__dirname, "..", "/dist"),
		filename : "static/js/[name].[hash].js",
		publicPath : "/Scenator"
	},
	resolve : {
		alias : {
			finger : path.resolve(__dirname, "..", "src/finger/"),
			navigationMenu : path.resolve(__dirname, "..", "src/UI/navigationMenu/"),
			sceneNavigator : path.resolve(__dirname, "..", "src/sceneNavigator/sceneNavigator.js"),
			area : path.resolve(__dirname, "..", "src/area"),
			"vue$" : "vue/dist/vue.esm.js"
		}
	},
	devtool : "source-map",
	externals : {
		"fs" : "fs"
	},
	plugins : [
		new MiniCssExtractPlugin({
			filename : "static/css/[name].[hash].css"
		}),
		new FileManagerPlugin({
			onEnd : {
				copy : [
					{
						source : path.resolve(__dirname, "../", "node_modules/web-socket-js"),
						destination : path.resolve(__dirname, "../dist", "static/js/web-socket-js")
					}
				]
			}
		}),
		new HTMLWebpackPlugin({
			title : "Scenator",
			filename : "index.html",
			template : "./main.handlebars",
			hash : false,
			chunks : ["vendors", "index"],
			// manual代表chunks的加载顺序直接按照上面数组的顺序
			chunksSortMode : "manual"
		}),
		new webpack.ProvidePlugin({
			$ : "jquery",
			jQuery : "jquery",
			"window.jQuery" : "jquery",
			"SystemJS" : "@scenator/systemjs",
			"window.SystemJS" : "@scenator/systemjs"
		}),
		new VueLoaderPlugin(),
		new HTMLWebpackPlugin({
			title : "SceneAuth",
			filename : "auth.html",
			template : "./src/sceneAuth/index.html",
			hash : false,
			chunks : ["sceneAuth"],
			// manual代表chunks的加载顺序直接按照上面数组的顺序
			chunksSortMode : "manual"
		})
	],
	module : {
		rules : [
			{
				test : /\.js$/,
				exclude : /node_modules/,
				loader : "eslint-loader",
				enforce : "pre",
				options : {
					configFile : ".eslintrc.json",
					cache : true,
					outputReport : {
						filePath : "checkstyle.html",
						formatter : require(require("eslint-html-reporter"))
					}
				}
			},
			{
				test : /\.handlebars$/,
				loader : "handlebars-loader"
			},
			{
				test : /\.css$/,
				use : [MiniCssExtractPlugin.loader, "css-loader"]
			},
			{
				test : /\.less$/,
				use : [MiniCssExtractPlugin.loader, "css-loader", "less-loader"]
			},
			{
				test : /\.png$|\.jpg$|\.gif$|\.swf$|\.IVE|\.3DS$|\.ico$/,
				loader : "file-loader?name=/static/images/[name]-[hash].[ext]"
			},
			{
				test : /\.eot$|\.woff$|\.woff2$|\.ttf$|\.svg$|\.swf$/,
				loader : "file-loader?name=/static/fonts/[name].[ext]"
			},
			{
				test : /\.js$/,
				include : [
					resolve("../appLoader.js"),
					resolve("../main.js"),
					resolve("../src")
				],
				use : {
					loader : "babel-loader"
				}
			},
			{
				test : /\.vue$/,
				loader : "vue-loader"
			}
		]
	},
	optimization : {
		minimizer : [new TerserPlugin({
			terserOptions : {
				compress : {
					drop_debugger : true,
					drop_console : false,
					keep_fnames : false
				},
				ecma : 5
			},
			sourceMap : true
		})],
		splitChunks : {
			chunks : "all", // 共有三个值可选：initial(初始模块)、async(按需加载模块)和all(全部模块)
			minSize : 30000, // 模块超过30k自动被抽离成公共模块
			minChunks : 1, // 模块被引用>=1次，便分割
			maxAsyncRequests : 5, // 异步加载chunk的并发请求数量<=5
			maxInitialRequests : 3, // 一个入口并发加载的chunk数量<=3
			name : true, // 默认由模块名+hash命名，名称相同时多个模块将合并为1个，可以设置为function
			automaticNameDelimiter : "~", // 命名分隔符
			cacheGroups : { // 缓存组，会继承和覆盖splitChunks的配置
				default : { // 模块缓存规则，设置为false，默认缓存组将禁用
					minChunks : 2, // 模块被引用>=2次，拆分至vendors公共模块
					priority : -20, // 优先级
					reuseExistingChunk : true // 默认使用已有的模块
				},
				vendors : {
					test : /[\\/]node_modules[\\/]/, // 表示默认拆分node_modules中的模块
					priority : -10
				},
				elementUI : {
					name : "chunk-elementUI", // 单独将 elementUI 拆包
					priority : 15, // 权重需大于其它缓存组
					test : /[\\/]node_modules[\\/]element-ui[\\/]/
				}
			}
		}
	}
};
module.exports = webpackConfig;