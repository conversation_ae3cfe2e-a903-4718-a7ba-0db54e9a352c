var path = require("path");
var FileManagerPlugin = require("filemanager-webpack-plugin");
function resolve(dir) {
	return path.join(__dirname, dir);
}

var webpackConfig = {
	entry : {
		"appServer" : ["./src/nodeServer/APPServer.js"],
		"exportExcel" : ["./src/tools/exportExcel/exportExcelNodeServer.js"],
		"mq" : ["./src/tools/mq/mqNodeServer.js"],
		"ws" : ["./src/tools/ws/wsNodeServer.js"]
	},
	output : {
		// path : path.join(__dirname, "..", "/dist/appListScanner"),
		// filename : "appListScanner.js"
		path : path.join(__dirname, "..", "/dist"),
		filename : "[name]/[name].js"
	},
	plugins : [
		new FileManagerPlugin({
			onEnd : {
				copy : [
					{
						source : "./src/tools/exportExcel/exportExcel.config.json",
						destination : path.resolve(__dirname, "../dist/exportExcel", "exportExcel.config.json")
					},
					{
						source : "./src/tools/exportExcel/start.bat",
						destination : path.resolve(__dirname, "../dist/exportExcel", "start.bat")
					},
					{
						source : "./src/tools/mq/mq.config.json",
						destination : path.resolve(__dirname, "../dist/mq", "mq.config.json")
					},
					{
						source : "./src/tools/mq/start.bat",
						destination : path.resolve(__dirname, "../dist/mq", "start.bat")
					},
					{
						source : "./src/tools/ws/ws.config.json",
						destination : path.resolve(__dirname, "../dist/ws", "ws.config.json")
					},
					{
						source : "./src/tools/ws/start.bat",
						destination : path.resolve(__dirname, "../dist/ws", "start.bat")
					},
					{
						source : "./src/UI/systemLogo/images",
						destination : path.resolve(__dirname, "../dist/images")
					},
					{
						source : "./src/UI/systemStyle/scenatorStyleImg",
						destination : path.resolve(__dirname, "../dist/images")
					},
					{
						source : "./src/UI/noSceneAuth/images",
						destination : path.resolve(__dirname, "../dist/images")
					},
					{
						source : "./src/UI/systemStyle/systemStyle.css",
						destination : path.resolve(__dirname, "../dist/styles", "systemStyle.css")
					},
					{
						source : "./src/UI/systemStyle/exportScss.scss",
						destination : path.resolve(__dirname, "../dist/styles", "exportScss.scss")
					},
					{
						source : "./src/UI/noSceneAuth/noSceneAuth.css",
						destination : path.resolve(__dirname, "../dist/styles", "noSceneAuth.css")
					}
				]
			}
		})
	],
	target : "node",
	module : {
		rules : [{
			test : /\.js$/,
			include : [
				resolve("APPServer")
			],
			use : {
				loader : "babel-loader"
			}
		}]
	},
	node : {
		__dirname : false
	}
};
module.exports = webpackConfig;