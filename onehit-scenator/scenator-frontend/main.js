var $ = require("jquery");
import _ from "lodash";
import SystemJS from "@scenator/systemjs";
import * as appLoader from "./src/appLoader/appLoader";
import {sceneNavigator} from "./src/sceneNavigator/sceneNavigator";
import {createNavigationMenus} from "./src/UI/navigationMenu";
import {getLogoMenu, setSystemLogo} from "./src/UI/systemLogo/index";
// 引入公共样式Js部分
import {systemStyle} from "./src/UI/systemStyle/systemStyle";
import {viewManager, modelManager, sceneManager, extensionManager, globalState, settingManager} from "finger";
import {recordLog} from "./src/services/server";
import {createI18next} from "./src/i18n";
// 添加全局的请求异常监听
import {createListening} from "./src/services/abnormalListening";
import eventBus from "./src/tools/eventBus.js";
createListening();

import "./src/themes/loading.css";
import "./src/themes/layout.css";
import "./src/themes/global.easyui.css";
import "./src/themes/global.ztree.css";
import UserInfoError from "./src/util/Error/UserInfoError";
import SceneError from "./src/util/Error/SceneError";



const ONEHIT_CONTEXT_PATH = window.contextPathManager.getContextPath();
const baseUrl = ONEHIT_CONTEXT_PATH + "/Scenator/resources/apps";

let currentModel;

const getAPPList = () => {
	var promise = new $.Deferred();
	$.ajax({
		type : "GET",
		url : ONEHIT_CONTEXT_PATH + "/Scenator/resources/apps/app.list.json",
		cache : false,
		success : function (response) {
			promise.resolve(response);
		},
		error : function (response) {
			promise.reject(response);
		}
	});
	return promise;
};

// 下面的逻辑不要修改
let req = XMLHttpRequest;
(function (open, send) {
	XMLHttpRequest.prototype.open = function () {
		open.apply(this, arguments);
	};
	XMLHttpRequest.prototype.send = function () {
		if (sessionStorage.getItem("platform")) {
			this.setRequestHeader("platform", sessionStorage.getItem("platform"));
		}
		send.apply(this, arguments);
	};
})(req.prototype.open, req.prototype.send);
// 方法名
const hasAppRegistered = function () {
	const hasViewProvider = Object.keys(viewManager.viewProviders).length !== 0;

	const hasModelProvider = Object.keys(modelManager.modelProviders).length !== 0;
	const hasAdapter = Object.keys(modelManager.adapters).length !== 0;
	const hasExtensionPoint = Object.keys(extensionManager.extensionPoints).length !== 0;

	return hasViewProvider || hasModelProvider || hasAdapter || hasExtensionPoint;
};

// 监听浏览器的高度变化，改变system_body的高度
const windowResize = function () {
	$(window).on("resize", function () {
		setTimeout(() => {
			$("#system_body").css("height", $(window).height() - $("#system_head").height() + "px");
		}, 100);
	});
};

function receiveMessage(event) {
	// 验证消息类型
	if(event.data.type !== "setCurrentModel") {return;}
		
	setCurrentModel(event.data.data);
}

async function setCurrentModel(data){
	const currentScene = sceneManager.getCurrentScene();
	if (currentScene && Object.keys(modelManager.modelProviders).length !== 0
		&& Object.keys(modelManager.modelProviders).findIndex(item => item === data.type) !== -1) {
		const model = await modelManager.request(data.type, data);
		currentScene.state.set("currentModel", [model]);
		currentModel = null;
	} else {
		currentModel = data;
	}
}

/**
 * 添加监听postMessage
 */
const listenPostMessage = () => {
	window.addEventListener("message", receiveMessage, false);
};

const userRoles = sessionStorage.getItem("userRoles");
console.log("当前用户角色：" + userRoles);

function getUrlParam() {
	const searchParam = new URLSearchParams(window.location.search);
	const hashParam = new URLSearchParams(window.location.hash.split("?")[1]);
	return searchParam ? searchParam : hashParam;
}

// 加载中
const createLoading = function (){
	let loadingHtml = `
	<div id="system_loading" class="loading-wrap">
		<div class="loading-item loading-active"></div>
		<span class="loading-text">正在加载系统语言包，请稍候...</span>
	</div>`;
	$("body").append(loadingHtml);
};
const changgLoading = function (loadingtMessage){
	$("#system_loading .loading-text").html(loadingtMessage);
};
// 加载完成
const loaded = function (){
	$("#system_loading").hide();
};

// 翻译必须前置，不然场景没有名称
createLoading();
createI18next().then((i18next) => {

	listenPostMessage();
	// const commonConfig = getCommonConfig();
	const commonConfig = settingManager.get("property", "commonProperty");
	// 给场景div添加高度，否则三维渲染时，会不停的撑开高度
	$("#system_body").css("height", $(window).height() - $("#system_head").height() + "px");
	changgLoading("正在加载系统插件，请稍候...");

	// 获取场景的URL，并解析参数
	const urlParam = getUrlParam();
	console.log(urlParam);
	let defaultSceneName = null;
	let modelFilter = {};
	let isFull = false;
	
	sessionStorage.setItem("platform", urlParam.get("platform"));
	if (urlParam.has("sceneName")) {
		defaultSceneName = urlParam.getItem("sceneName");
		modelFilter.name = urlParam.getItem("modelName");
		modelFilter.modelLabelKey = urlParam.getItem("modelLabelKey");
		modelFilter.modelLabelValue = urlParam.getItem("modelLabelValue");
	}
	if (urlParam.has("isFull") && urlParam.getItem("isFull") === "true"){
		isFull = true;
		$("#system_head").hide();
		$("#system_body").css("height", $(window).height() + "px");
	}
	

	Promise
		.all([
			settingManager.get("app.list.json", "app.list.json"),
			sceneNavigator.getEntries(userRoles, defaultSceneName, modelFilter),
			sceneNavigator.getSceneStructures(),
			getLogoMenu()
		])
		.then(async (values) => {
			const APP_LIST_INDEX = 0;
			const removedValues = _.remove(values, (value, index) => index === APP_LIST_INDEX);
			let appList = [];
			if (!_.isEmpty(removedValues)) {
				appList = _.clone(removedValues[0]);
				const loadAppStyles = (apps) => {
					if (_.isEmpty(apps)) {
						return false;
					}
					_.map(apps, (app) => {
						if (!_.isEmpty(app.CSSURLS)) {
							_.forEach(app.CSSURLS, function (CSSURL) {
								appLoader.loadStyle(baseUrl + CSSURL);
							});
						}
					});
					/**
					 * 加载所有App的样式之后，加载系统默认的样式
					 */
					appLoader.loadStyle(ONEHIT_CONTEXT_PATH + "/Scenator/resources/static/styles/"
						+ "systemStyle.css?time=" + new Date().getTime());
					if (isFull) {
						appLoader.loadStyle(ONEHIT_CONTEXT_PATH + "/Scenator/resources/static/styles/"
						+ "fullStyle.css?time=" + new Date().getTime());
					}
				};
				// 样式加载提前
				changgLoading("正在加载系统样式，请稍候...");
				loadAppStyles(appList.asyncApps);
				loadAppStyles(appList.syncApps);
			}
			values = _.flatten(_.compact(values));

			changgLoading("正在加载系统导航，请稍候...");
			if (!_.isEmpty(values)) {
				// 场景初始化 和 场景呈现分离
				const {sceneNavMenu, funNavMenu, logoMenu} = createNavigationMenus(values);
				// 场景菜单初始化
				sceneNavMenu.init(commonConfig.hideNav, isFull);
				// 监听修改场景菜单
				eventBus.$on("UPDATE_SCENE_MENU", (params) => {
					sceneNavMenu.updateMenu(params);
				});
				eventBus.$on("ADD_SCENE_MENU", (params) => {
					sceneNavMenu.addExtractMenu(params);
				});

				// LOGO
				logoMenu.init();
				setSystemLogo();

				// 系统初始化公共样式
				systemStyle();
				// 提前渲染场景模板
				const currentScene = sceneNavigator.getCurrentScene();
				if (currentScene && _.isFunction(currentScene.renderTemplate)) {
					await currentScene.renderTemplate();
				}
				appLoader.initSystemJs();

				// 注册i18next
				i18next[Symbol.toStringTag] = "module";
				SystemJS.set("i18next", i18next);
				// TODO 引入app和注册app逻辑拆分，step，complete
				changgLoading("正在解析系统插件，请稍候...");
				appLoader.importAppsAsynchronously(appList.asyncApps, () => {
					appLoader.importAppsSynchronously(appList.syncApps, async () => {
						if (!hasAppRegistered()) {
							const msg = "没有任何app注册成功，请检查app是否满足标准";
							recordLog("error", msg);
							throw new Error(msg);
						} else {
							loaded();
							
							funNavMenu.addExtraMenuItems();
							sceneNavMenu.selectDefaultMenuItem();
							const platform = sessionStorage.getItem("platform");
							if (platform) {
								globalState.set("platform", platform);
							}
							const currentScene = sceneManager.getCurrentScene();
							if (currentScene && currentModel) {
								const model = await modelManager.request(currentModel.type, currentModel);
								currentScene.state.set("currentModel", [model]);
								currentModel = null;
							}
						}
					});
				});
			} else {
				const ERROR_MSG = "scenes文件夹内无场景文件，请检查配置是否正确";
				recordLog("error", ERROR_MSG);
			}
			windowResize();
		})
		.catch(error => {
			let errorMsg = i18next.t("error.serviceInitFailed");
			if (error instanceof UserInfoError || error instanceof SceneError) {
				errorMsg = error.message;
			}
			recordLog("error", errorMsg);
		});
});