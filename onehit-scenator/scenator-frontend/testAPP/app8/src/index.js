import * as app1 from "app1";

class App8View {
	render(container){
		const app1ViewType = app1.viewProviders[0].getViewType();

		container.innerHTML = `App8渲染，拿到了App1的viewType：${app1ViewType}`;
	}

	addModel(){}

	beforeDestroy(){}
}

class App8ViewProvider{

	getViewType() {
		return "App8View";
	}

	request(options) {
		return new App8View();
	}
}

const appProvider = new App8ViewProvider();

export default {
	viewProviders : [appProvider]
};