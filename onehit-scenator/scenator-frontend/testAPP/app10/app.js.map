{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///webpack/bootstrap e61cb3e1a453ccd93002", "webpack:///./src/Model/ViewProvider.js", "webpack:///./src/Model/View.js", "webpack:///./src/index.js", "webpack:///external \"jquery\""], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;ACVA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA,mDAA2C,cAAc;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,mCAA2B,0BAA0B,EAAE;AACvD,yCAAiC,eAAe;AAChD;AACA;AACA;;AAEA;AACA,8DAAsD,+DAA+D;;AAErH;AACA;;AAEA;AACA;;;;;;;;;AChEA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,C;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA,C;;;;;;;;;;;ACfA;AACA;;AAEA;AACA;AACA,G;;;;;;ACLA,+C", "file": "app.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"jquery\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"jquery\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"app10\"] = factory(require(\"jquery\"));\n\telse\n\t\troot[\"app10\"] = factory(root[\"jquery\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE_3__) {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// identity function for calling harmony imports with the correct context\n \t__webpack_require__.i = function(value) { return value; };\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"./\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 2);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap e61cb3e1a453ccd93002", "import View from \"./View\";\r\n\r\nexport default class ViewProvider{\r\n\r\n\tgetViewType() {\r\n\t\treturn \"View20\";\r\n\t}\r\n\r\n\trequest(options) {\r\n\t\treturn new View(options);\r\n\t}\r\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/Model/ViewProvider.js\n// module id = 0\n// module chunks = 0", "import $ from \"jquery\";\r\n// eslint-disable-next-line no-undef\r\nadd1();\r\nexport default class View {\r\n\tconstructor() {\r\n\r\n\t}\r\n\t\r\n\trender(container){\r\n\t\tconst $container = $(container);\r\n\t}\r\n\r\n\taddModel(){}\r\n\r\n\tbeforeDestroy(){}\r\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/Model/View.js\n// module id = 1\n// module chunks = 0", "import ViewProvider from \"./Model/ViewProvider\";\r\nconst viewProvider10 = new ViewProvider();\r\n\r\nexport default {\r\n\tviewProviders : [viewProvider10]\r\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/index.js\n// module id = 2\n// module chunks = 0", "module.exports = __WEBPACK_EXTERNAL_MODULE_3__;\n\n\n//////////////////\n// WEBPACK FOOTER\n// external \"jquery\"\n// module id = 3\n// module chunks = 0"], "sourceRoot": ""}