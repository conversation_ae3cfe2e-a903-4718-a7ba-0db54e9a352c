var path = require("path");
function resolve(dir) {
	return path.join(__dirname, dir);
}

module.exports = {
	entry : {
		"index" : "./src/index.js"
	},
	output : {
		path : path.join(__dirname, "/app4"),
		filename : "app.js",
		publicPath : "./",
		libraryTarget : "umd",
		library : "app4"
	},
	devtool : "source-map",
	externals : {
		app1 : "app1",
		app5 : "app5"
	},
	module : {
		rules : [
			{
				test : /\.js$/,
				include : [
					resolve("src")
				],
				use : {
					loader : "babel-loader"
				}
			}
		]
	}
};
