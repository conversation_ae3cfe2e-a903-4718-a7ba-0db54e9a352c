import * as app5 from "app5";
import * as app1 from "app1";

class App4View {
	render(container){
		const app1ViewType = app1.viewProviders[0].getViewType();
		const app5ViewType = app5.viewProviders[0].getViewType();

		container.innerHTML = `App4渲染，拿到了App1的viewType：${app1ViewType}，和App5的viewType：${app5ViewType}`;
	}

	addModel(){}

	beforeDestroy(){}
}

class App4ViewProvider{

	getViewType() {
		return "App4View";
	}

	request(options) {
		return new App4View();
	}
}

const appProvider = new App4ViewProvider();

export default {
	viewProviders : [appProvider]
};