import * as App2 from "app2";

class App7View {
	render(container){
		const app2ViewType = App2.viewProviders[0].getViewType();

		container.innerHTML = `App7渲染，拿到了App2的viewType：${app2ViewType}`;
	}

	addModel(){}

	beforeDestroy(){}
}

class App7ViewProvider{

	getViewType() {
		return "App7View";
	}

	request(options) {
		return new App7View();
	}
}

const appProvider = new App7ViewProvider();

export default {
	viewProviders : [appProvider]
};