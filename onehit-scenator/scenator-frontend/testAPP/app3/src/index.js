import * as App2 from "app2";

class App3View {
	render(container){
		const app2ViewType = App2.viewProviders[0].getViewType();

		container.innerHTML = `App3渲染，拿到了App2的viewType：${app2ViewType}`;
	}

	addModel(){}

	beforeDestroy(){}
}

class App3ViewProvider{

	getViewType() {
		return "App3View";
	}

	request(options) {
		return new App3View();
	}
}

const appProvider = new App3ViewProvider();

export default {
	viewProviders : [appProvider]
};