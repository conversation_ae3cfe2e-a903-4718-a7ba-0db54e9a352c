var webpack = require("webpack");

var path = require("path");
function resolve(dir) {
	return path.join(__dirname, dir);
}

module.exports = {
	entry : {
		"index" : "./src/index.js"
	},
	output : {
		path : path.join(__dirname, "/app3"),
		filename : "app.js",
		publicPath : "./",
		libraryTarget : "umd",
		library : "app3"
	},
	devtool : "source-map",
	externals : {
		app2 : "app2"
	},
	module : {
		rules : [
			{
				test : /\.js$/,
				include : [
					resolve("src")
				],
				use : {
					loader : "babel-loader"
				}
			}
		]
	}
};
