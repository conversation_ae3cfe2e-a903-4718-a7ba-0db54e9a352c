import Adapter from "./adapter";
import ModelProvider from "./model";
import Extension from "./extension";
import ExtensionPoint from "./extensionPoint";


class App1View {
	constructor(){
		this.viewModel = [];
	}

	render(container){
		const viewModelType = this.viewModel[0].getType();

		container.innerHTML = `App1渲染了，viewModelType为${viewModelType}`;
	}

	addModel(model, options){
		this.viewModel.push(adapter.adapt(model, options));
	}

	beforeDestroy(){}
}


class App1ViewProvider{

	getViewType() {
		return "App1View";
	}

	request(options) {
		return new App1View();
	}
}

const appProvider = new App1ViewProvider();
const adapter = new Adapter();
const modelProvider = new ModelProvider();
const extensionPoint = new ExtensionPoint();
const extension = new Extension();

export default {
	viewProviders : [appProvider],
	adapters : [adapter],
	modelProviders : [modelProvider],
	extensions : [extension],
	extensionPoints : [extensionPoint]
};