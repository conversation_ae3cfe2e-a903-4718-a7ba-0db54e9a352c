{"plugins": [], "recurseDepth": 10, "source": {"include": ["src"], "includePattern": ".+\\.js(doc|x)?$", "excludePattern": "(^|\\/|\\\\)_"}, "sourceType": "module", "tags": {"allowUnknownTags": true, "dictionaries": ["jsdoc", "closure"]}, "opts": {"template": "node_modules/docdash", "encoding": "utf8", "destination": "docs/", "recurse": true, "verbose": true}, "templates": {"cleverLinks": false, "monospaceLinks": false}}