import * as App4 from "app4";

class App6View {
	render(container){
		const app4ViewType = App4.viewProviders[0].getViewType();

		container.innerHTML = `App6渲染，拿到了App4的viewType：${app4ViewType}`;
	}

	addModel(){}

	beforeDestroy(){}
}

class App6ViewProvider{

	getViewType() {
		return "App6View";
	}

	request(options) {
		return new App6View();
	}
}

const appProvider = new App6ViewProvider();

export default {
	viewProviders : [appProvider]
};