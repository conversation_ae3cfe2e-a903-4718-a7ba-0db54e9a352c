import Model from "./Model";

/**
 * 请求创建并获取Model
 * @interface ModelProvider
 */
export default class ModelProvider {
	/**
	 * 获取能够创建的模型的类型
	 *
	 * @returns {String} 模型的类型
	 * @memberof ModelProvider
	 */
	getModelType() {
		return "Model5";
	}

	/**
	 * 创建并获取特定的Model实例
	 *
	 * @param {Map<String, String>} [options] 构建模型的参数
	 * @returns {Promise<Model[]>} 创建并获取到的Model实例集合
	 * @memberof ModelProvider
	 */
	request(options) {
		return [new Model(options)];
	}
}