import viewTemplate from "../handlebars/view.handlebars";
import {modelManager} from "finger";
import $ from "jquery";
export default class View {
	constructor() {
		this.$container = null;
		this.viewModel = null;
	}
	
	render(container){
		this.$container = $(container);
		this.$container.html(viewTemplate());

		if (this.viewModel) {
			$(".wrapper-view-5 .content").html(this.viewModel.names);
		}
		// 添加点击监听
		$(".wrapper-view-5 #changeColor_red").click((e) => {
			$(".wrapper-view-5 .text").css({
				color : "red"
			});
		});
		$(".wrapper-view-5 #changeColor_black").click((e) => {
			$(".wrapper-view-5 .text").css({
				color : "#000000"
			});
		});
	}

	addModel(model){
		const viewModel = modelManager.adapt(model, "View5Model");
		this.viewModel = viewModel;
	}

	beforeDestroy(){}
}