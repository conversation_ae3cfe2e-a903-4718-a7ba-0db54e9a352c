import ViewModel from "./ViewModel";
/**
 * 适配器，负责Model和ViewModel的双向适配
 */
export default class Adapter {
	/**
	 * 获取能够转换的Model的类型
	 *
	 * @returns {String} Model的类型
	 * @memberof Adapter
	 */
	getModelType() {
		return "Model5";
	}

	/**
	 * 获取能够转换的ViewModel的类型
	 *
	 * @returns {String} ViewModel的类型
	 * @memberof Adapter
	 */
	getViewModelType() {
		return "View5Model";
	}

	/**
	 * Model与ViewModel的适配
	 *
	 * @param {Model|ViewModel} originModel 需要转换的模型
	 * @param {Objcet} [options] 适配过程中的参数
	 * @returns {Promise<ViewModel|Model>} 转换后的模型实例
	 * @memberof Adapter
	 */
	adapt(originModel, options) {
		// 只管Model→ViewModel
		return new ViewModel(originModel);
	}
}