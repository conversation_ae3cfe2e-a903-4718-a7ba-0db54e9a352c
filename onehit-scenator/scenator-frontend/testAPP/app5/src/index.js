import ModelProvider from "./Class/ModelProvider";
import ViewProvider from "./Class/ViewProvider";
import Adapter from "./Class/Adapter";
import Extension from "./Class/Extension";
import ExtensionPoint from "./Class/ExtensionPoint";
const modelProviders = [new ModelProvider()];
const viewProviders = [new ViewProvider()];
const adapters = [new Adapter];
const extensionPoints = [new ExtensionPoint()];
const extensions = [new Extension()];

export default {
	modelProviders,
	adapters,
	viewProviders,
	extensions,
	extensionPoints
};