const FileManagerPlugin = require("filemanager-webpack-plugin");
var path = require("path");
function resolve(dir) {
	return path.join(__dirname, dir);
}

const devAppPath = "G:/plugin/EDC-Scenator/third-server/scenator/apps";

module.exports = {
	entry : {
		"index" : "./src/index.js"
	},
	output : {
		path : path.join(devAppPath, "./", "/app5"),
		filename : "app.js",
		publicPath : "./",
		libraryTarget : "umd",
		library : "app5"
	},
	devtool : "source-map",
	externals : {
		jquery : "jquery",
		finger : "finger"
	},
	module : {
		rules : [
			{
				test : /\.js$/,
				include : [
					resolve("src")
				],
				use : {
					loader : "babel-loader"
				}
			},
			{
				test : /\.handlebars$/,
				loader : "handlebars-loader"
			}
		]
	},
	plugins : [
		new FileManagerPlugin({
			onEnd : {
				copy : [
					{
						source : path.resolve(__dirname, "../", "readme.json"), 
						destination : path.resolve(devAppPath, "./app5", "readme.json")
					}
				]
			}
		})
	]
};
