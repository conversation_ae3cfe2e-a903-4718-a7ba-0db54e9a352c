const webpack = require("webpack");
const FileManagerPlugin = require("filemanager-webpack-plugin");
var path = require("path");
function resolve(dir) {
	return path.join(__dirname, dir);
}

module.exports = {
	entry : {
		"index" : "./src/index.js"
	},
	output : {
		path : path.join(__dirname, "../", "/dist"),
		filename : "app.js",
		publicPath : "./",
		libraryTarget : "umd",
		library : "app5"
	},
	devtool : "source-map",
	externals : {
		jquery : "jquery",
		finger : "finger"
	},
	module : {
		rules : [
			{
				test : /\.js$/,
				include : [
					resolve("../src")
				],
				use : {
					loader : "babel-loader"
				}
			},
			{
				test : /\.handlebars$/,
				loader : "handlebars-loader"
			}
		]
	},
	plugins : [
		new FileManagerPlugin({
			onEnd : {
				copy : [
					{
						source : path.resolve(__dirname, "../", "readme.json"), 
						destination : path.resolve(__dirname, "../dist", "readme.json")
					}
				]
			}
		}),
		new webpack.optimize.UglifyJsPlugin({
			compress : {
				warnings : false,
				drop_debugger : true,
				drop_console : true
			},
			sourceMap : true
		})
	]
};