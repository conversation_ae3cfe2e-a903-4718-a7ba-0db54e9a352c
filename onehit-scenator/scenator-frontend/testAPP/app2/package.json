{"name": "app2", "version": "0.1.0", "description": "", "main": "src/index.js", "license": "ISC", "scripts": {"build": "webpack"}, "peerDependencies": {"app1": "0.1.0", "app4": "0.1.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-loader": "^7.0.6", "babel-plugin-transform-async-to-generator": "^6.24.1", "babel-plugin-transform-es2015-arrow-functions": "^6.22.0", "babel-plugin-transform-es2015-template-literals": "^6.22.0", "babel-preset-env": "^1.7.0", "webpack": "2.6.1"}}