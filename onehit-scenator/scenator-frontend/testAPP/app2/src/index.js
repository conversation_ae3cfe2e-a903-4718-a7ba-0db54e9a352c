import * as app1 from "app1";
import * as app4 from "app4";

class App2View {

	render(container){
		const app1ViewType = app1.viewProviders[0].getViewType();
		const app4ViewType = app4.viewProviders[0].getViewType();

		container.innerHTML = `App2渲染，拿到了App1的viewType：${app1ViewType}，和App4的viewType：${app4ViewType}`;
	}

	addModel(){}

	beforeDestroy(){}
}

class App2ViewProvider{

	getViewType() {
		return "App2View";
	}

	request(options) {
		return new App2View();
	}
}

const appProvider = new App2ViewProvider();

export default {
	viewProviders : [appProvider]
};