var webpack = require("webpack");

var path = require("path");
function resolve(dir) {
	return path.join(__dirname, dir);
}

module.exports = {
	entry : {
		"index" : "./src/index.js"
	},
	output : {
		path : path.join(__dirname, "/app2"),
		filename : "app.js",
		publicPath : "./",
		libraryTarget : "umd",
		library : "app2"
	},
	devtool : "source-map",
	externals : {
		app1 : "app1",
		app4 : "app4"
	},
	module : {
		rules : [
			{
				test : /\.js$/,
				include : [
					resolve("src")
				],
				use : {
					loader : "babel-loader"
				}
			}
		]
	}
};
