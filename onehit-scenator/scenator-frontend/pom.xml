<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>onehit-scenator</artifactId>
        <groupId>com.rzon.onehit</groupId>
        <version>${onehit.version}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>${onehit.version}</version>
    <artifactId>scenator-frontend</artifactId>

    <properties>
        <frontend-maven-plugin.version>1.9.1</frontend-maven-plugin.version>
        <npmRegistryURL>https://scm.rzon.tech/nexus/repository/npm-public/</npmRegistryURL>
    </properties>

    <build>
        <plugins>
            <plugin>

                <groupId>com.github.eirslett</groupId>
                <artifactId>frontend-maven-plugin</artifactId>
                <version>${frontend-maven-plugin.version}</version>

                <!-- 全局配置 -->
                <configuration>
                    <npmInheritsProxyConfigFromMaven>false</npmInheritsProxyConfigFromMaven>
                    <installDirectory>target</installDirectory>
                    <npmExecutable>${project.basedir}/node/node_modules/npm/bin/npm-cli.js</npmExecutable>
                </configuration>

                <executions>
                    <!-- Install our node and npm version to run npm/node scripts-->
                    <execution>
                        <id>install node and npm</id>
                        <goals>
                            <goal>install-node-and-npm</goal>
                        </goals>
                        <configuration>
                            <nodeVersion>v12.14.1</nodeVersion>
                            <npmVersion>6.13.4</npmVersion>
                            <!-- optional: where to download node from. Defaults to https://nodejs.org/dist/ -->
                            <nodeDownloadRoot>http://npmmirror.com/mirrors/node/</nodeDownloadRoot>
                        </configuration>
                    </execution>
                    <!-- Install all project dependencies -->
                    <execution>
                        <id>npm install</id>
                        <goals>
                            <goal>npm</goal>
                        </goals>
                        <!-- optional: default phase is "generate-resources" -->
                        <phase>generate-resources</phase>
                        <!-- Optional configuration which provides for running any npm command -->
                        <configuration>
                            <arguments>install</arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>build</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>npm</goal>
                        </goals>
                        <configuration>
                            <arguments>run build-contextPath --engine-strict</arguments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>